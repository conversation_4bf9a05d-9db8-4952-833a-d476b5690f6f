16:05:30 **** Incremental Build of configuration Debug for project 48V code ****
make -j14 all 
'Building file: ../01_SWC/HeatingMatControl/HeatingMatControl.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"01_SWC/HeatingMatControl/HeatingMatControl.d" -MT"01_SWC/HeatingMatControl/HeatingMatControl.o" -c -o "01_SWC/HeatingMatControl/HeatingMatControl.o" "../01_SWC/HeatingMatControl/HeatingMatControl.c"
'Finished building: ../01_SWC/HeatingMatControl/HeatingMatControl.c'
' '
'Building target: 48V code.elf'
'Invoking: GNU Arm Cross C Linker'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -T "D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M\GCC\Z20K116M_flash.ld" -Xlinker --gc-sections -Wl,-Map,"48V code.map" --specs=nosys.specs -o "48V code.elf"  ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.o  ./03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.o  ./03_BSW/System/03_MCAL/Gpt.o ./03_BSW/System/03_MCAL/Mcu.o ./03_BSW/System/03_MCAL/Wdg.o  ./03_BSW/System/02_HAL/Wdgif.o  ./03_BSW/System/01_Service/Dem.o ./03_BSW/System/01_Service/EcuM.o ./03_BSW/System/01_Service/OS.o ./03_BSW/System/01_Service/Wdgm.o  ./03_BSW/STAR/Std_Types.o ./03_BSW/STAR/main.o  ./03_BSW/Memory/03_MCAL/Fls.o  ./03_BSW/Memory/02_HAL/Fee.o ./03_BSW/Memory/02_HAL/MemIf.o  ./03_BSW/Memory/01_Service/NvM.o  ./03_BSW/IO/03_MCAL/Adc.o ./03_BSW/IO/03_MCAL/Dio.o ./03_BSW/IO/03_MCAL/Pwm.o  ./03_BSW/IO/02_HAL/AdcIf.o ./03_BSW/IO/02_HAL/DioIf.o ./03_BSW/IO/02_HAL/PwmIf.o  ./02_RTE/Rte_HeatingMatControl.o ./02_RTE/Rte_SafetyMonitor.o  ./01_SWC/SafetyMonitor/SafetyMonitor.o  ./01_SWC/HeatingMatControl/HeatingMatControl.o  ./.metadata/.plugins/org.eclipse.cdt.make.core/specs.o   
'Finished building target: 48V code.elf'
' '
'Invoking: GNU Arm Cross Create Flash Image'
'Invoking: GNU Arm Cross Print Size'
arm-none-eabi-size --format=berkeley "48V code.elf"
arm-none-eabi-objcopy -O ihex "48V code.elf"  "48V code.hex"
   text	   data	    bss	    dec	    hex	filename
  42684	      0	   2780	  45464	   b198	48V code.elf
'Finished building: 48V code.siz'
'Finished building: 48V code.hex'
' '
' '
