16:23:17 **** Incremental Build of configuration Debug for project 48V code ****
make -j14 all 
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.c"
'Invoking: GNU Arm Cross C Compiler'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.c"
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.c'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.c"
' '
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.c'
' '
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.c'
' '
'Invoking: GNU Arm Cross C Compiler'
' '
' '
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.c"
' '
' '
' '
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.c"
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.c"
'Invoking: GNU Arm Cross C Compiler'
' '
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.c'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.c'
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.c'
' '
' '
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.c'
' '
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.c'
' '
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.c'
' '
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c'
'Invoking: GNU Arm Cross C Compiler'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c"
'Invoking: GNU Arm Cross C Compiler'
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.c"
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c'
'Invoking: GNU Arm Cross C Compiler'
' '
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.c'
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.c'
' '
'Building file: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.c"
'Building file: ../03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.d" -MT"03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.o" -c -o "03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.o" "../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.c"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.c'
'Invoking: GNU Arm Cross Assembler'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -x assembler-with-cpp -MMD -MP -MF"03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.d" -MT"03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.o" -c -o "03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.o" "../03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S"
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.c'
' '
' '
' '
'Building file: ../03_BSW/System/03_MCAL/Gpt.c'
'Building file: ../03_BSW/System/03_MCAL/Mcu.c'
'Building file: ../03_BSW/System/03_MCAL/Wdg.c'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.c'
'Finished building: ../03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/03_MCAL/Gpt.d" -MT"03_BSW/System/03_MCAL/Gpt.o" -c -o "03_BSW/System/03_MCAL/Gpt.o" "../03_BSW/System/03_MCAL/Gpt.c"
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/03_MCAL/Mcu.d" -MT"03_BSW/System/03_MCAL/Mcu.o" -c -o "03_BSW/System/03_MCAL/Mcu.o" "../03_BSW/System/03_MCAL/Mcu.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/03_MCAL/Wdg.d" -MT"03_BSW/System/03_MCAL/Wdg.o" -c -o "03_BSW/System/03_MCAL/Wdg.o" "../03_BSW/System/03_MCAL/Wdg.c"
' '
'Building file: ../03_BSW/System/02_HAL/Wdgif.c'
'Building file: ../03_BSW/System/01_Service/Dem.c'
'Building file: ../03_BSW/System/01_Service/EcuM.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/02_HAL/Wdgif.d" -MT"03_BSW/System/02_HAL/Wdgif.o" -c -o "03_BSW/System/02_HAL/Wdgif.o" "../03_BSW/System/02_HAL/Wdgif.c"
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/01_Service/Dem.d" -MT"03_BSW/System/01_Service/Dem.o" -c -o "03_BSW/System/01_Service/Dem.o" "../03_BSW/System/01_Service/Dem.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/01_Service/EcuM.d" -MT"03_BSW/System/01_Service/EcuM.o" -c -o "03_BSW/System/01_Service/EcuM.o" "../03_BSW/System/01_Service/EcuM.c"
'Finished building: ../03_BSW/System/03_MCAL/Gpt.c'
' '
'Building file: ../03_BSW/System/01_Service/OS.c'
'Finished building: ../03_BSW/System/03_MCAL/Wdg.c'
'Finished building: ../03_BSW/System/03_MCAL/Mcu.c'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/System/01_Service/Dem.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/01_Service/OS.d" -MT"03_BSW/System/01_Service/OS.o" -c -o "03_BSW/System/01_Service/OS.o" "../03_BSW/System/01_Service/OS.c"
' '
' '
'Finished building: ../03_BSW/System/02_HAL/Wdgif.c'
' '
'Building file: ../03_BSW/System/01_Service/Wdgm.c'
' '
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/System/01_Service/Wdgm.d" -MT"03_BSW/System/01_Service/Wdgm.o" -c -o "03_BSW/System/01_Service/Wdgm.o" "../03_BSW/System/01_Service/Wdgm.c"
'Finished building: ../03_BSW/System/01_Service/EcuM.c'
'Building file: ../03_BSW/STAR/Std_Types.c'
' '
'Building file: ../03_BSW/STAR/main.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/STAR/Std_Types.d" -MT"03_BSW/STAR/Std_Types.o" -c -o "03_BSW/STAR/Std_Types.o" "../03_BSW/STAR/Std_Types.c"
'Building file: ../03_BSW/Memory/03_MCAL/Fls.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/STAR/main.d" -MT"03_BSW/STAR/main.o" -c -o "03_BSW/STAR/main.o" "../03_BSW/STAR/main.c"
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/Memory/03_MCAL/Fls.d" -MT"03_BSW/Memory/03_MCAL/Fls.o" -c -o "03_BSW/Memory/03_MCAL/Fls.o" "../03_BSW/Memory/03_MCAL/Fls.c"
'Finished building: ../03_BSW/System/01_Service/Wdgm.c'
'Building file: ../03_BSW/Memory/02_HAL/Fee.c'
'Finished building: ../03_BSW/System/01_Service/OS.c'
' '
'Invoking: GNU Arm Cross C Compiler'
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/Memory/02_HAL/Fee.d" -MT"03_BSW/Memory/02_HAL/Fee.o" -c -o "03_BSW/Memory/02_HAL/Fee.o" "../03_BSW/Memory/02_HAL/Fee.c"
'Building file: ../03_BSW/Memory/02_HAL/MemIf.c'
'Finished building: ../03_BSW/STAR/Std_Types.c'
'Building file: ../03_BSW/Memory/01_Service/NvM.c'
'Invoking: GNU Arm Cross C Compiler'
' '
'Finished building: ../03_BSW/Memory/03_MCAL/Fls.c'
'Finished building: ../03_BSW/STAR/main.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/Memory/02_HAL/MemIf.d" -MT"03_BSW/Memory/02_HAL/MemIf.o" -c -o "03_BSW/Memory/02_HAL/MemIf.o" "../03_BSW/Memory/02_HAL/MemIf.c"
'Invoking: GNU Arm Cross C Compiler'
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/Memory/01_Service/NvM.d" -MT"03_BSW/Memory/01_Service/NvM.o" -c -o "03_BSW/Memory/01_Service/NvM.o" "../03_BSW/Memory/01_Service/NvM.c"
' '
'Building file: ../03_BSW/IO/03_MCAL/Adc.c'
'Building file: ../03_BSW/IO/03_MCAL/Dio.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/IO/03_MCAL/Adc.d" -MT"03_BSW/IO/03_MCAL/Adc.o" -c -o "03_BSW/IO/03_MCAL/Adc.o" "../03_BSW/IO/03_MCAL/Adc.c"
'Invoking: GNU Arm Cross C Compiler'
'Building file: ../03_BSW/IO/03_MCAL/Pwm.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/IO/03_MCAL/Dio.d" -MT"03_BSW/IO/03_MCAL/Dio.o" -c -o "03_BSW/IO/03_MCAL/Dio.o" "../03_BSW/IO/03_MCAL/Dio.c"
'Building file: ../03_BSW/IO/02_HAL/AdcIf.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/Memory/02_HAL/Fee.c'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/IO/03_MCAL/Pwm.d" -MT"03_BSW/IO/03_MCAL/Pwm.o" -c -o "03_BSW/IO/03_MCAL/Pwm.o" "../03_BSW/IO/03_MCAL/Pwm.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/IO/02_HAL/AdcIf.d" -MT"03_BSW/IO/02_HAL/AdcIf.o" -c -o "03_BSW/IO/02_HAL/AdcIf.o" "../03_BSW/IO/02_HAL/AdcIf.c"
'Finished building: ../03_BSW/Memory/02_HAL/MemIf.c'
' '
' '
'Building file: ../03_BSW/IO/02_HAL/DioIf.c'
'Building file: ../03_BSW/IO/02_HAL/PwmIf.c'
'Building file: ../02_RTE/Rte_HeatingMatControl.c'
'Finished building: ../03_BSW/Memory/01_Service/NvM.c'
'Finished building: ../03_BSW/IO/03_MCAL/Adc.c'
'Finished building: ../03_BSW/IO/03_MCAL/Dio.c'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/IO/03_MCAL/Pwm.c'
'Finished building: ../03_BSW/IO/02_HAL/AdcIf.c'
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
' '
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/IO/02_HAL/DioIf.d" -MT"03_BSW/IO/02_HAL/DioIf.o" -c -o "03_BSW/IO/02_HAL/DioIf.o" "../03_BSW/IO/02_HAL/DioIf.c"
' '
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"03_BSW/IO/02_HAL/PwmIf.d" -MT"03_BSW/IO/02_HAL/PwmIf.o" -c -o "03_BSW/IO/02_HAL/PwmIf.o" "../03_BSW/IO/02_HAL/PwmIf.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"02_RTE/Rte_HeatingMatControl.d" -MT"02_RTE/Rte_HeatingMatControl.o" -c -o "02_RTE/Rte_HeatingMatControl.o" "../02_RTE/Rte_HeatingMatControl.c"
' '
'Building file: ../02_RTE/Rte_SafetyMonitor.c'
'Building file: ../01_SWC/SafetyMonitor/SafetyMonitor.c'
'Building file: ../01_SWC/HeatingMatControl/HeatingMatControl.c'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"02_RTE/Rte_SafetyMonitor.d" -MT"02_RTE/Rte_SafetyMonitor.o" -c -o "02_RTE/Rte_SafetyMonitor.o" "../02_RTE/Rte_SafetyMonitor.c"
'Invoking: GNU Arm Cross C Compiler'
'Invoking: GNU Arm Cross C Compiler'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"01_SWC/SafetyMonitor/SafetyMonitor.d" -MT"01_SWC/SafetyMonitor/SafetyMonitor.o" -c -o "01_SWC/SafetyMonitor/SafetyMonitor.o" "../01_SWC/SafetyMonitor/SafetyMonitor.c"
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"01_SWC/HeatingMatControl/HeatingMatControl.d" -MT"01_SWC/HeatingMatControl/HeatingMatControl.o" -c -o "01_SWC/HeatingMatControl/HeatingMatControl.o" "../01_SWC/HeatingMatControl/HeatingMatControl.c"
'Building file: ../.metadata/.plugins/org.eclipse.cdt.make.core/specs.c'
'Finished building: ../03_BSW/IO/02_HAL/DioIf.c'
'Invoking: GNU Arm Cross C Compiler'
'Finished building: ../03_BSW/IO/02_HAL/PwmIf.c'
' '
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF".metadata/.plugins/org.eclipse.cdt.make.core/specs.d" -MT".metadata/.plugins/org.eclipse.cdt.make.core/specs.o" -c -o ".metadata/.plugins/org.eclipse.cdt.make.core/specs.o" "../.metadata/.plugins/org.eclipse.cdt.make.core/specs.c"
'Finished building: ../02_RTE/Rte_HeatingMatControl.c'
' '
' '
'Finished building: ../01_SWC/SafetyMonitor/SafetyMonitor.c'
'Finished building: ../02_RTE/Rte_SafetyMonitor.c'
'Finished building: ../01_SWC/HeatingMatControl/HeatingMatControl.c'
'Finished building: ../.metadata/.plugins/org.eclipse.cdt.make.core/specs.c'
' '
' '
' '
' '
'Building target: 48V code.elf'
'Invoking: GNU Arm Cross C Linker'
arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -T "D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M\GCC\Z20K116M_flash.ld" -Xlinker --gc-sections -Wl,-Map,"48V code.map" --specs=nosys.specs -o "48V code.elf"  ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.o ./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.o  ./03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.o  ./03_BSW/System/03_MCAL/Gpt.o ./03_BSW/System/03_MCAL/Mcu.o ./03_BSW/System/03_MCAL/Wdg.o  ./03_BSW/System/02_HAL/Wdgif.o  ./03_BSW/System/01_Service/Dem.o ./03_BSW/System/01_Service/EcuM.o ./03_BSW/System/01_Service/OS.o ./03_BSW/System/01_Service/Wdgm.o  ./03_BSW/STAR/Std_Types.o ./03_BSW/STAR/main.o  ./03_BSW/Memory/03_MCAL/Fls.o  ./03_BSW/Memory/02_HAL/Fee.o ./03_BSW/Memory/02_HAL/MemIf.o  ./03_BSW/Memory/01_Service/NvM.o  ./03_BSW/IO/03_MCAL/Adc.o ./03_BSW/IO/03_MCAL/Dio.o ./03_BSW/IO/03_MCAL/Pwm.o  ./03_BSW/IO/02_HAL/AdcIf.o ./03_BSW/IO/02_HAL/DioIf.o ./03_BSW/IO/02_HAL/PwmIf.o  ./02_RTE/Rte_HeatingMatControl.o ./02_RTE/Rte_SafetyMonitor.o  ./01_SWC/SafetyMonitor/SafetyMonitor.o  ./01_SWC/HeatingMatControl/HeatingMatControl.o  ./.metadata/.plugins/org.eclipse.cdt.make.core/specs.o   
'Finished building target: 48V code.elf'
' '
'Invoking: GNU Arm Cross Create Flash Image'
'Invoking: GNU Arm Cross Print Size'
arm-none-eabi-objcopy -O ihex "48V code.elf"  "48V code.hex"
arm-none-eabi-size --format=berkeley "48V code.elf"
   text	   data	    bss	    dec	    hex	filename
  42588	      0	   2780	  45368	   b138	48V code.elf
'Finished building: 48V code.siz'
'Finished building: 48V code.hex'
' '
' '
