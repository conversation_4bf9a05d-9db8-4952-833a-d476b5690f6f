################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.c \
../03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.c 

OBJS += \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.o \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.o 

C_DEPS += \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_adc.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_can.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_clock.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_cmp.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_crc.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_dma.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_drv.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_ewdt.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_flash.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_gpio.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_hwdiv.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_i2c.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_pmu.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_regfile.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_rtc.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_spi.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_srmc.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_stim.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_sysctrl.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tdg.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tim.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_tmu.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_uart.d \
./03_BSW/ZhiXinSDK/StdDriver/Src/Z20K11xM_wdog.d 


# Each subdirectory must supply rules for building sources it contributes
03_BSW/ZhiXinSDK/StdDriver/Src/%.o: ../03_BSW/ZhiXinSDK/StdDriver/Src/%.c 03_BSW/ZhiXinSDK/StdDriver/Src/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU Arm Cross C Compiler'
	arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


