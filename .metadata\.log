!SESSION 2025-08-25 09:49:47.572 -----------------------------------------------
eclipse.buildId=4.36.0.20250605-1300
java.version=21.0.7
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Framework arguments:  -product org.eclipse.epp.package.embedcpp.product
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -product org.eclipse.epp.package.embedcpp.product

This is a continuation of log file D:\NN\Project\48V\.metadata\.bak_0.log
Created Time: 2025-08-28 09:29:49.613

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.617
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.620
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.626
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.629
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.634
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.634
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.634
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.634
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.649
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.649
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.MenuItem.releaseChildren(MenuItem.java:521)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Menu.releaseChildren(Menu.java:945)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:903)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:233)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.649
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.menus.CommandContributionItem.disconnectReferences(CommandContributionItem.java:730)
	at org.eclipse.ui.menus.CommandContributionItem.setParent(CommandContributionItem.java:673)
	at org.eclipse.jface.action.ContributionManager.itemRemoved(ContributionManager.java:403)
	at org.eclipse.jface.action.ContributionManager.removeAll(ContributionManager.java:448)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:245)
	at org.eclipse.jface.action.MenuManager.dispose(MenuManager.java:247)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.e4.ui.workbench.swt 4 2 2025-08-28 09:29:49.711
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench.swt".
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.MenuItem.setText(MenuItem.java:1045)
	at org.eclipse.ui.menus.CommandContributionItem.updateMenuItem(CommandContributionItem.java:525)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:486)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:479)
	at org.eclipse.ui.menus.CommandContributionItem.lambda$0(CommandContributionItem.java:951)
	at org.eclipse.jface.bindings.BindingManager.fireBindingManagerChanged(BindingManager.java:892)
	at org.eclipse.jface.bindings.BindingManager.setActiveBindings(BindingManager.java:2171)
	at org.eclipse.jface.bindings.BindingManager.recomputeBindings(BindingManager.java:1730)
	at org.eclipse.jface.bindings.BindingManager.contextManagerChanged(BindingManager.java:691)
	at org.eclipse.core.commands.contexts.ContextManager.fireContextManagerChanged(ContextManager.java:164)
	at org.eclipse.core.commands.contexts.ContextManager.setActiveContextIds(ContextManager.java:295)
	at org.eclipse.e4.ui.services.ContextServiceAddon$1.changed(ContextServiceAddon.java:49)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:105)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.processScheduled(EclipseContext.java:358)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.set(EclipseContext.java:374)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.activate(EclipseContext.java:661)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener$2.run(ShellActivationListener.java:137)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.activate(ShellActivationListener.java:133)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.handleEvent(ShellActivationListener.java:76)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.filterEvent(Display.java:1340)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1213)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1223)
	at org.eclipse.swt.widgets.Decorations.WM_ACTIVATE(Decorations.java:1535)
	at org.eclipse.swt.widgets.Shell.WM_ACTIVATE(Shell.java:2414)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4776)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:336)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1494)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2387)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5106)
	at org.eclipse.swt.internal.win32.OS.BringWindowToTop(Native Method)
	at org.eclipse.swt.widgets.Decorations.bringToTop(Decorations.java:215)
	at org.eclipse.swt.widgets.Shell.open(Shell.java:1293)
	at org.eclipse.jface.window.Window.open(Window.java:795)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.741
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.745
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:29:49.767
!MESSAGE Unhandled event loop exception
!STACK 0
java.lang.NullPointerException: Cannot invoke "org.eclipse.ui.commands.ICommandService.unregisterElement(org.eclipse.ui.commands.IElementReference)" because "this.fParentService" is null
	at org.eclipse.ui.internal.commands.SlaveCommandService.unregisterElement(SlaveCommandService.java:245)
	at org.eclipse.ui.internal.commands.SlaveCommandService.lambda$0(SlaveCommandService.java:286)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.handleWidgetDispose(HandledContributionItem.java:350)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.lambda$2(AbstractContributionItem.java:492)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.jface.action.ToolBarManager.update(ToolBarManager.java:324)
	at org.eclipse.e4.ui.workbench.renderers.swt.ToolBarManagerRenderer.updateWidget(ToolBarManagerRenderer.java:637)
	at org.eclipse.e4.ui.workbench.renderers.swt.ToolBarManagerRenderer.subscribeUIElementTopicVisible(ToolBarManagerRenderer.java:219)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:56)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:183)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:133)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4870)
	at org.eclipse.e4.ui.workbench.swt.DisplayUISynchronize.syncExec(DisplayUISynchronize.java:34)
	at org.eclipse.e4.ui.internal.di.UIEventObjectSupplier$UIEventHandler.handleEvent(UIEventObjectSupplier.java:64)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:206)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:201)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:151)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:132)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:73)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:48)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:55)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:60)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:424)
	at org.eclipse.e4.ui.model.application.ui.impl.UIElementImpl.setVisible(UIElementImpl.java:365)
	at org.eclipse.e4.ui.workbench.renderers.swt.ToolBarContributionRecord.updateVisibility(ToolBarContributionRecord.java:79)
	at org.eclipse.e4.ui.workbench.renderers.swt.ToolBarManagerRenderer$2.changed(ToolBarManagerRenderer.java:519)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:105)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.processScheduled(EclipseContext.java:358)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.set(EclipseContext.java:374)
	at org.eclipse.e4.ui.internal.services.ContextContextService.deactivateContext(ContextContextService.java:140)
	at org.eclipse.ui.internal.contexts.ContextService.deactivateContext(ContextService.java:190)
	at org.eclipse.debug.internal.ui.contexts.DebugModelContextBindingManager$1.runInUIThread(DebugModelContextBindingManager.java:343)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.e4.ui.workbench.swt 4 2 2025-08-28 09:30:10.849
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench.swt".
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.MenuItem.setText(MenuItem.java:1045)
	at org.eclipse.ui.menus.CommandContributionItem.updateMenuItem(CommandContributionItem.java:523)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:486)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:479)
	at org.eclipse.ui.menus.CommandContributionItem.lambda$0(CommandContributionItem.java:951)
	at org.eclipse.jface.bindings.BindingManager.fireBindingManagerChanged(BindingManager.java:892)
	at org.eclipse.jface.bindings.BindingManager.setActiveBindings(BindingManager.java:2171)
	at org.eclipse.jface.bindings.BindingManager.recomputeBindings(BindingManager.java:1730)
	at org.eclipse.jface.bindings.BindingManager.contextManagerChanged(BindingManager.java:691)
	at org.eclipse.core.commands.contexts.ContextManager.fireContextManagerChanged(ContextManager.java:164)
	at org.eclipse.core.commands.contexts.ContextManager.setActiveContextIds(ContextManager.java:295)
	at org.eclipse.e4.ui.services.ContextServiceAddon$1.changed(ContextServiceAddon.java:49)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:105)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.processScheduled(EclipseContext.java:358)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.set(EclipseContext.java:374)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.activate(EclipseContext.java:661)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.activateBranch(EclipseContext.java:670)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener$1.run(ShellActivationListener.java:106)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.processWindow(ShellActivationListener.java:102)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.handleEvent(ShellActivationListener.java:65)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.filterEvent(Display.java:1340)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1213)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1223)
	at org.eclipse.swt.widgets.Decorations.WM_ACTIVATE(Decorations.java:1535)
	at org.eclipse.swt.widgets.Shell.WM_ACTIVATE(Shell.java:2414)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4776)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:336)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1494)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2387)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5106)
	at org.eclipse.swt.internal.win32.OS.PeekMessage(Native Method)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3719)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:11.098
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:11.101
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.e4.ui.workbench.swt 4 2 2025-08-28 09:30:12.639
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench.swt".
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.MenuItem.setText(MenuItem.java:1045)
	at org.eclipse.ui.menus.CommandContributionItem.updateMenuItem(CommandContributionItem.java:523)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:486)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:479)
	at org.eclipse.ui.menus.CommandContributionItem.lambda$0(CommandContributionItem.java:951)
	at org.eclipse.jface.bindings.BindingManager.fireBindingManagerChanged(BindingManager.java:892)
	at org.eclipse.jface.bindings.BindingManager.setActiveBindings(BindingManager.java:2171)
	at org.eclipse.jface.bindings.BindingManager.recomputeBindings(BindingManager.java:1730)
	at org.eclipse.jface.bindings.BindingManager.contextManagerChanged(BindingManager.java:691)
	at org.eclipse.core.commands.contexts.ContextManager.fireContextManagerChanged(ContextManager.java:164)
	at org.eclipse.core.commands.contexts.ContextManager.setActiveContextIds(ContextManager.java:295)
	at org.eclipse.e4.ui.services.ContextServiceAddon$1.changed(ContextServiceAddon.java:49)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:105)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.processScheduled(EclipseContext.java:358)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.set(EclipseContext.java:374)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.activate(EclipseContext.java:661)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener$2.run(ShellActivationListener.java:137)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.activate(ShellActivationListener.java:133)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.handleEvent(ShellActivationListener.java:76)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.filterEvent(Display.java:1340)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1213)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1223)
	at org.eclipse.swt.widgets.Decorations.WM_ACTIVATE(Decorations.java:1535)
	at org.eclipse.swt.widgets.Shell.WM_ACTIVATE(Shell.java:2414)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4776)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:336)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1494)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2387)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5106)
	at org.eclipse.swt.internal.win32.OS.PeekMessage(Native Method)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3719)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:12.652
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:12.652
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:16.623
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.MenuItem.setText(MenuItem.java:1045)
	at org.eclipse.ui.menus.CommandContributionItem.updateMenuItem(CommandContributionItem.java:525)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:486)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:479)
	at org.eclipse.ui.menus.CommandContributionItem.lambda$0(CommandContributionItem.java:951)
	at org.eclipse.jface.bindings.BindingManager.fireBindingManagerChanged(BindingManager.java:892)
	at org.eclipse.jface.bindings.BindingManager.setActiveBindings(BindingManager.java:2171)
	at org.eclipse.jface.bindings.BindingManager.recomputeBindings(BindingManager.java:1730)
	at org.eclipse.jface.bindings.BindingManager.contextManagerChanged(BindingManager.java:691)
	at org.eclipse.core.commands.contexts.ContextManager.fireContextManagerChanged(ContextManager.java:164)
	at org.eclipse.core.commands.contexts.ContextManager.removeActiveContext(ContextManager.java:245)
	at org.eclipse.ui.internal.contexts.ContextAuthority.updateContext(ContextAuthority.java:695)
	at org.eclipse.ui.internal.contexts.ContextAuthority.deactivateContext(ContextAuthority.java:325)
	at org.eclipse.ui.internal.contexts.ContextAuthority$1.widgetDisposed(ContextAuthority.java:260)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:161)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1219)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:896)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.swt.widgets.Decorations.dispose(Decorations.java:376)
	at org.eclipse.jface.window.Window.close(Window.java:335)
	at org.eclipse.jface.dialogs.Dialog.close(Dialog.java:983)
	at org.eclipse.jface.dialogs.MessageDialog.buttonPressed(MessageDialog.java:274)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.buttonPressed(InternalErrorDialog.java:101)
	at org.eclipse.jface.dialogs.Dialog.lambda$0(Dialog.java:615)
	at org.eclipse.swt.events.SelectionListener$1.widgetSelected(SelectionListener.java:83)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:286)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4136)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3724)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.e4.ui.workbench.swt 4 2 2025-08-28 09:30:16.642
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench.swt".
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.MenuItem.setText(MenuItem.java:1045)
	at org.eclipse.ui.menus.CommandContributionItem.updateMenuItem(CommandContributionItem.java:523)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:486)
	at org.eclipse.ui.menus.CommandContributionItem.update(CommandContributionItem.java:479)
	at org.eclipse.ui.menus.CommandContributionItem.lambda$0(CommandContributionItem.java:951)
	at org.eclipse.jface.bindings.BindingManager.fireBindingManagerChanged(BindingManager.java:892)
	at org.eclipse.jface.bindings.BindingManager.setActiveBindings(BindingManager.java:2171)
	at org.eclipse.jface.bindings.BindingManager.recomputeBindings(BindingManager.java:1730)
	at org.eclipse.jface.bindings.BindingManager.contextManagerChanged(BindingManager.java:691)
	at org.eclipse.core.commands.contexts.ContextManager.fireContextManagerChanged(ContextManager.java:164)
	at org.eclipse.core.commands.contexts.ContextManager.setActiveContextIds(ContextManager.java:295)
	at org.eclipse.e4.ui.services.ContextServiceAddon$1.changed(ContextServiceAddon.java:49)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:105)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.processScheduled(EclipseContext.java:358)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.set(EclipseContext.java:374)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.activate(EclipseContext.java:661)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.activateBranch(EclipseContext.java:670)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener$1.run(ShellActivationListener.java:106)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.processWindow(ShellActivationListener.java:102)
	at org.eclipse.e4.ui.internal.workbench.swt.ShellActivationListener.handleEvent(ShellActivationListener.java:65)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.filterEvent(Display.java:1340)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1213)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1238)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1223)
	at org.eclipse.swt.widgets.Decorations.WM_ACTIVATE(Decorations.java:1535)
	at org.eclipse.swt.widgets.Shell.WM_ACTIVATE(Shell.java:2414)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4776)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:336)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1494)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2387)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5106)
	at org.eclipse.swt.internal.win32.OS.DestroyWindow(Native Method)
	at org.eclipse.swt.widgets.Control.destroyWidget(Control.java:740)
	at org.eclipse.swt.widgets.Shell.destroyWidget(Shell.java:737)
	at org.eclipse.swt.widgets.Widget.release(Widget.java:913)
	at org.eclipse.swt.widgets.Widget.dispose(Widget.java:485)
	at org.eclipse.swt.widgets.Decorations.dispose(Decorations.java:376)
	at org.eclipse.jface.window.Window.close(Window.java:335)
	at org.eclipse.jface.dialogs.Dialog.close(Dialog.java:983)
	at org.eclipse.jface.dialogs.MessageDialog.buttonPressed(MessageDialog.java:274)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.buttonPressed(InternalErrorDialog.java:101)
	at org.eclipse.jface.dialogs.Dialog.lambda$0(Dialog.java:615)
	at org.eclipse.swt.events.SelectionListener$1.widgetSelected(SelectionListener.java:83)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:286)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:91)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4338)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1214)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4136)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3724)
	at org.eclipse.jface.window.Window.runEventLoop(Window.java:823)
	at org.eclipse.jface.window.Window.open(Window.java:799)
	at org.eclipse.jface.dialogs.MessageDialog.open(MessageDialog.java:608)
	at org.eclipse.ui.internal.ide.dialogs.InternalErrorDialog.open(InternalErrorDialog.java:85)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.openQuestionDialog(IDEWorkbenchErrorHandler.java:184)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.handleException(IDEWorkbenchErrorHandler.java:143)
	at org.eclipse.ui.internal.ide.IDEWorkbenchErrorHandler.lambda$0(IDEWorkbenchErrorHandler.java:111)
	at org.eclipse.ui.progress.UIJob$1.runInUIThread(UIJob.java:56)
	at org.eclipse.ui.progress.UIJob.lambda$0(UIJob.java:148)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:16.661
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)

!ENTRY org.eclipse.ui 4 0 2025-08-28 09:30:16.669
!MESSAGE Unhandled event loop exception
!STACK 0
org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4945)
	at org.eclipse.swt.SWT.error(SWT.java:4860)
	at org.eclipse.swt.SWT.error(SWT.java:4831)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:507)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:426)
	at org.eclipse.swt.widgets.Widget.getData(Widget.java:601)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.updateIcons(AbstractContributionItem.java:190)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:125)
	at org.eclipse.e4.ui.workbench.renderers.swt.AbstractContributionItem.update(AbstractContributionItem.java:120)
	at org.eclipse.e4.ui.workbench.renderers.swt.HandledContributionItem.lambda$2(HandledContributionItem.java:174)
	at org.eclipse.swt.widgets.RunnableLock.run(RunnableLock.java:40)
	at org.eclipse.swt.widgets.Synchronizer.runAsyncMessages(Synchronizer.java:132)
	at org.eclipse.swt.widgets.Display.runAsyncMessages(Display.java:4111)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3727)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$5.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1042)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:153)
	at org.eclipse.ui.internal.Workbench.lambda$3(Workbench.java:678)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:339)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:583)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:173)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:185)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:219)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:149)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:115)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:467)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:298)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:627)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:575)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1431)
