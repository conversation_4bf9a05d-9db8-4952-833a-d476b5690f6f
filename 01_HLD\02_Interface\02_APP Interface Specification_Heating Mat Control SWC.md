### 4.1 Heating Mat Control SWC

#### 4.1.1 Overview
The Heating Mat Control SWC is responsible for managing the heating mat temperature control, power management, and operational modes based on the software requirements specification. It implements a state machine with four core states (INIT, OFF, ACTIVE, FAULT) and supports five heating levels with PID closed-loop control. The module provides interfaces for heating level control, status monitoring, communication with LIN bus, and configuration management through NvM services.

#### 4.1.2 System State Machine
The module implements the following state machine as per requirements:
- **HMC_STATE_INIT**: System initialization and self-check
- **HMC_STATE_OFF**: Heating disabled, PWM output = 0%
- **HMC_STATE_ACTIVE**: Active heating with PID control
- **HMC_STATE_FAULT**: Fault state, heating disabled

#### 4.1.3 Runnable Functions

```c
/* Initialization and configuration */
FUNC(Std_ReturnType, CODETYPE) HeatingControl_Init(void);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_LoadConfiguration(void);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_SaveConfiguration(void);

/* Heating level control operations */
FUNC(Std_ReturnType, CODETYPE) HeatingControl_SetHeatingLevel(VAR(HeatingControl_LevelType, VARTYPE) level);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetHeatingLevel(P2VAR(HeatingControl_LevelType, PTR2VARTYPE, PTRTYPE) level);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_ProcessHeatingRequest(VAR(HeatingControl_LevelType, VARTYPE) levelRequest, VAR(HeatingControl_ActiveRequestType, VARTYPE) activeRequest);

/* Temperature monitoring operations */
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetCurrentTemperature(P2VAR(DataValue_Type, PTR2VARTYPE, PTRTYPE) temperatureData);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetTargetTemperature(P2VAR(uint8, PTR2VARTYPE, PTRTYPE) temperature);

/* System state and status operations */
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetSystemState(P2VAR(HeatingControl_StateType, PTR2VARTYPE, PTRTYPE) state);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetSystemStatus(P2VAR(HeatingControl_StatusType, PTR2VARTYPE, PTRTYPE) status);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetFaultStatus(P2VAR(HeatingControl_FaultType, PTR2VARTYPE, PTRTYPE) faultStatus);

/* Timer and safety operations */
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetRemainingTime(P2VAR(uint16, PTR2VARTYPE, PTRTYPE) remainingTime);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_ResetTimer(void);

/* Configuration and calibration operations */
FUNC(Std_ReturnType, CODETYPE) HeatingControl_SetPIDParameters(VAR(uint16, VARTYPE) kp, VAR(uint16, VARTYPE) ki, VAR(uint16, VARTYPE) kd);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_GetPIDParameters(P2VAR(HeatingControl_PIDParamsType, PTR2VARTYPE, PTRTYPE) pidParams);
FUNC(Std_ReturnType, CODETYPE) HeatingControl_SetLevelTemperatures(P2CONST(HeatingControl_LevelTempConfigType, PTR2VARTYPE, PTRTYPE) levelConfig);

/* Main processing function (100ms cycle as per requirements) */
FUNC(void, CODETYPE) HeatingControl_Runnable(void);
```

#### 4.1.4 Port Interfaces

##### 4.1.4.1 Sender-Receiver Interfaces

```c
/* LIN Communication Input Signals (from COM Interface via RTE) */
typedef struct {
    VAR(HeatingControl_LevelType, VARTYPE) heatingLevelRequest;     /* Heating level request (0=OFF, 1=L1, 2=L2, 3=L3, 4=L4, 5=L5) */
    VAR(HeatingControl_ActiveRequestType, VARTYPE) heatingActiveRequest; /* Heating active request (0=Nothing, 1=ON, 2=OFF) */
    VAR(boolean, VARTYPE) passengerPresent;                         /* Passenger presence signal for auto shutoff */
    DataStatus_Type signalStatus;                                   /* Signal validity status */
} HeatingControl_LINInputType;

/* LIN Communication Output Signals (to COM Interface via RTE) */
typedef struct {
    VAR(HeatingControl_StatusType, VARTYPE) heatingMatStatus;       /* Heating mat status (0=Off, 1=Active, 2=Fault) */
    VAR(HeatingControl_LevelType, VARTYPE) heatingLevelRes;         /* Current heating level response */
    VAR(uint8, VARTYPE) currentTemperature;                         /* Current temperature for monitoring */
    VAR(uint16, VARTYPE) remainingTime;                             /* Remaining heating time in minutes */
} HeatingControl_LINOutputType;

/* Temperature Sensor Input (from ADC interface via RTE) */
typedef struct {
    VAR(uint8, VARTYPE) temperatureValue;       /* Temperature value in Celsius */
    DataStatus_Type temperatureStatus;          /* Temperature sensor status */
} HeatingControl_TemperatureInputType;

/* Safety Monitor Input (from Safety Monitor SWC) */
typedef struct {
    VAR(boolean, VARTYPE) safetyFaultActive;           /* Overall safety fault status */
    VAR(boolean, VARTYPE) temperatureOverLimit;        /* Temperature over limit fault (>90°C or <-40°C) */
    VAR(boolean, VARTYPE) heatingMatOpenCircuit;       /* Heating mat open circuit fault (<20mA) */
    VAR(boolean, VARTYPE) heatingMatShortCircuit;      /* Heating mat short circuit fault (>3A) */
    VAR(boolean, VARTYPE) voltageOverLimit;            /* Voltage over limit fault (>60V) */
    VAR(boolean, VARTYPE) voltageUnderLimit;           /* Voltage under limit fault (<36V) */
    VAR(uint8, VARTYPE) safetyMonitorState;            /* Safety monitor state (0=INIT, 1=NORMAL, 2=FAULT) */
    DataStatus_Type safetyDataStatus;                  /* Safety data validity status */
} HeatingControl_SafetyInputType;

/* PWM Control Output (to PWM Driver via RTE) */
typedef struct {
    VAR(uint8, VARTYPE) pwmDutyCycle;          /* PWM duty cycle (0-100) */
    VAR(boolean, VARTYPE) pwmEnable;            /* PWM output enable flag */
} HeatingControl_PWMOutputType;

/* NvM Data Interface */
typedef struct {
    VAR(HeatingControl_LevelType, VARTYPE) lastHeatingLevel;       /* Last used heating level */
    VAR(boolean, VARTYPE) heatingFunctionEnabled;                  /* Heating function enable flag */
    VAR(boolean, VARTYPE) timerModeEnabled;                        /* Timer mode configuration */
} HeatingControl_NvMDataType;
```

##### 4.1.4.2 Client-Server Interfaces

```c
/* NvM Service Interface */
typedef struct {
    FUNC(Std_ReturnType, CODETYPE) (*ReadBlock)(VAR(uint16, VARTYPE) blockId, P2VAR(void, PTR2VARTYPE, PTRTYPE) dataPtr);
    FUNC(Std_ReturnType, CODETYPE) (*WriteBlock)(VAR(uint16, VARTYPE) blockId, P2CONST(void, PTR2VARTYPE, PTRTYPE) dataPtr);
    FUNC(Std_ReturnType, CODETYPE) (*GetErrorStatus)(VAR(uint16, VARTYPE) blockId, P2VAR(uint8, PTR2VARTYPE, PTRTYPE) requestResult);
} HeatingControl_NvMServiceType;

/* PWM Service Interface */
typedef struct {
    FUNC(Std_ReturnType, CODETYPE) (*SetDutyCycle)(VAR(uint8, VARTYPE) channelId, VAR(uint16, VARTYPE) dutyCycle);
    FUNC(Std_ReturnType, CODETYPE) (*SetOutputToIdle)(VAR(uint8, VARTYPE) channelId);
    FUNC(Std_ReturnType, CODETYPE) (*GetOutputState)(VAR(uint8, VARTYPE) channelId, P2VAR(boolean, PTR2VARTYPE, PTRTYPE) outputState);
} HeatingControl_PWMServiceType;

/* Diagnostic Service Interface (UDS) */
typedef struct {
    FUNC(Std_ReturnType, CODETYPE) (*ReadDataByIdentifier)(VAR(uint16, VARTYPE) dataId, P2VAR(uint8, PTR2VARTYPE, PTRTYPE) dataPtr, P2VAR(uint16, PTR2VARTYPE, PTRTYPE) dataLength);
    FUNC(Std_ReturnType, CODETYPE) (*WriteDataByIdentifier)(VAR(uint16, VARTYPE) dataId, P2CONST(uint8, PTR2VARTYPE, PTRTYPE) dataPtr, VAR(uint16, VARTYPE) dataLength);
    FUNC(Std_ReturnType, CODETYPE) (*RoutineControl)(VAR(uint16, VARTYPE) routineId, VAR(uint8, VARTYPE) subFunction);
} HeatingControl_DiagnosticServiceType;
```

#### 4.1.5 Data Types

##### 4.1.5.1 Enumeration Types

```c
/* Heating mat state enumeration */
typedef enum {
    HMC_STATE_INIT = 0,                     /* Heating initialization state */
    HMC_STATE_OFF,                          /* Heating disabled, PWM = 0% */
    HMC_STATE_ACTIVE,                       /* Active heating with PID control */
    HMC_STATE_FAULT                         /* Fault state, heating disabled */
} HeatingControl_StateType;

/* Heating level enumeration */
typedef enum {
    HEATING_LEVEL_OFF = 0,                  /* Heating disabled */
    HEATING_LEVEL_1,                        /* Level 1: 34°C */
    HEATING_LEVEL_2,                        /* Level 2: 36°C */
    HEATING_LEVEL_3,                        /* Level 3: 38°C */
    HEATING_LEVEL_4,                        /* Level 4: 40°C */
    HEATING_LEVEL_5                         /* Level 5: 42°C */
} HeatingControl_LevelType;

/* Heating active request enumeration */
typedef enum {
    HEATING_ACTIVE_NOTHING = 0,             /* No request */
    HEATING_ACTIVE_ON,                      /* Turn heating on */
    HEATING_ACTIVE_OFF                      /* Turn heating off */
} HeatingControl_ActiveRequestType;

/* System status enumeration (for LIN communication) */ 
typedef enum {
    HEATING_STATUS_OFF = 0,                 /* Heating mat off */
    HEATING_STATUS_ACTIVE,                  /* Heating mat active */
    HEATING_STATUS_FAULT                    /* Heating mat fault */
} HeatingControl_StatusType;

/* Fault type enumeration - Aligned with Safety Monitor SWC requirements */
typedef enum {
    HEATING_CONTROL_FAULT_NONE = 0x00,                      /* No faults */
    HEATING_CONTROL_FAULT_TEMPERATURE_OVER_LIMIT = 0x01,    /* Temperature over limit (>90°C or <-40°C) */
    HEATING_CONTROL_FAULT_HEATING_MAT_OPEN = 0x02,          /* Heating mat open circuit (<20mA) */
    HEATING_CONTROL_FAULT_HEATING_MAT_SHORT = 0x04,         /* Heating mat short circuit (>3A) */
    HEATING_CONTROL_FAULT_VOLTAGE_OVER_LIMIT = 0x08,        /* Voltage over limit (>60V) */
    HEATING_CONTROL_FAULT_VOLTAGE_UNDER_LIMIT = 0x10,       /* Voltage under limit (<36V) */
    HEATING_CONTROL_FAULT_TEMPERATURE_SENSOR = 0x20,        /* Temperature sensor fault */
    HEATING_CONTROL_FAULT_PWM_OUTPUT = 0x40,                /* PWM output fault */
    HEATING_CONTROL_FAULT_COMMUNICATION = 0x80,             /* Communication fault */
    HEATING_CONTROL_FAULT_NVM_ACCESS = 0x100,               /* NvM access fault */
    HEATING_CONTROL_FAULT_CONFIGURATION = 0x200,            /* Configuration fault */
    HEATING_CONTROL_FAULT_SAFETY_MONITOR_GENERAL = 0x400    /* General safety monitor fault */
} HeatingControl_FaultType;

/* Timer mode enumeration */
typedef enum {
    HEATING_TIMER_DISABLED = 0,             /* Timer disabled, no auto shutoff */
    HEATING_TIMER_30MIN,                    /* 30 minutes timer mode */
    HEATING_TIMER_PASSENGER_BASED           /* Passenger presence based shutoff */
} HeatingControl_TimerModeType;
```

##### 4.1.5.2 Structure Types

```c
/* PID control parameters (configurable via UDS) */
typedef struct {
    VAR(uint16, VARTYPE) pidKp;             /* PID proportional gain (scaled by 100) */
    VAR(uint16, VARTYPE) pidKi;             /* PID integral gain (scaled by 100) */
    VAR(uint16, VARTYPE) pidKd;             /* PID derivative gain (scaled by 100) */
} HeatingControl_PIDParamsType;

/* Heating level temperature configuration (configurable via UDS) */
typedef struct {
    VAR(uint8, VARTYPE) level1Temp;         /* Level 1 target temperature (default: 34°C) */
    VAR(uint8, VARTYPE) level2Temp;         /* Level 2 target temperature (default: 36°C) */
    VAR(uint8, VARTYPE) level3Temp;         /* Level 3 target temperature (default: 38°C) */
    VAR(uint8, VARTYPE) level4Temp;         /* Level 4 target temperature (default: 40°C) */
    VAR(uint8, VARTYPE) level5Temp;         /* Level 5 target temperature (default: 42°C) */
} HeatingControl_LevelTempConfigType;

/* System configuration structure */
typedef struct {
    VAR(boolean, VARTYPE) heatingFunctionEnabled;          /* Heating function enable flag */
    VAR(HeatingControl_TimerModeType, VARTYPE) timerMode;   /* Timer mode configuration */
    VAR(uint16, VARTYPE) maxHeatingTime;                    /* Maximum heating time (seconds) */
    HeatingControl_PIDParamsType pidParams;                 /* PID control parameters */
    HeatingControl_LevelTempConfigType levelTemps;          /* Level temperature configuration */
} HeatingControl_ConfigType;

/* System runtime data structure */
typedef struct {
    VAR(HeatingControl_StateType, VARTYPE) currentState;           /* Current system state */
    VAR(HeatingControl_LevelType, VARTYPE) currentLevel;           /* Current heating level */
    VAR(uint8, VARTYPE) currentTemperature;                        /* Current temperature */
    VAR(uint8, VARTYPE) targetTemperature;                         /* Target temperature */
    VAR(uint16, VARTYPE) currentPWMDutyCycle;                      /* Current PWM duty cycle */
    VAR(uint16, VARTYPE) heatingTimer;                             /* Heating timer (seconds) */
    VAR(boolean, VARTYPE) heatingActive;                           /* Heating active flag */
    VAR(HeatingControl_FaultType, VARTYPE) activeFaults;           /* Active fault flags */
} HeatingControl_RuntimeDataType;

/* PID control internal data structure */
typedef struct {
    VAR(sint16, VARTYPE) previousError;            /* Previous error value */
    VAR(sint32, VARTYPE) integralSum;              /* Integral sum */
    VAR(sint16, VARTYPE) derivativeError;          /* Derivative error */
    VAR(uint32, VARTYPE) lastUpdateTime;           /* Last update timestamp */
} HeatingControl_PIDDataType;
```

#### 4.1.6 Constants

```c
/* Heating level temperature defaults (as per requirements) */
#define HEATING_CONTROL_LEVEL_1_TEMP            34u     /* Level 1 temperature (°C) */
#define HEATING_CONTROL_LEVEL_2_TEMP            36u     /* Level 2 temperature (°C) */
#define HEATING_CONTROL_LEVEL_3_TEMP            38u     /* Level 3 temperature (°C) */
#define HEATING_CONTROL_LEVEL_4_TEMP            40u     /* Level 4 temperature (°C) */
#define HEATING_CONTROL_LEVEL_5_TEMP            42u     /* Level 5 temperature (°C) */

/* Temperature limits */
#define HEATING_CONTROL_MIN_TEMP                0u      /* Minimum temperature (°C) */
#define HEATING_CONTROL_MAX_TEMP                60u     /* Maximum temperature (°C) */
#define HEATING_CONTROL_DEFAULT_TEMP            25u     /* Default temperature (°C) */

/* PWM control parameters */
#define HEATING_CONTROL_PWM_MIN_DUTY            0u      /* Minimum PWM duty cycle */
#define HEATING_CONTROL_PWM_MAX_DUTY            100u    /* Maximum PWM duty cycle */

/* Timing constants */
#define HEATING_CONTROL_MAIN_CYCLE_TIME         100u    /* Main function cycle time (ms) */
#define HEATING_CONTROL_MAX_HEATING_TIME        1800u   /* Maximum heating time (30 minutes in seconds) */
#define HEATING_CONTROL_TIMER_RESOLUTION        1000u   /* Timer resolution (ms) */

/* PID control default parameters */
#define HEATING_CONTROL_PID_KP_DEFAULT          100u    /* Default Kp (scaled by 100) */
#define HEATING_CONTROL_PID_KI_DEFAULT          50u     /* Default Ki (scaled by 100) */
#define HEATING_CONTROL_PID_KD_DEFAULT          25u     /* Default Kd (scaled by 100) */

/* Safety and fault parameters */
#define HEATING_CONTROL_FAULT_DEBOUNCE_TIME     500u    /* Fault debounce time (ms) */
#define HEATING_CONTROL_TEMP_HYSTERESIS         2u      /* Temperature hysteresis (°C) */

/* NvM configuration */
#define HEATING_CONTROL_NVM_BLOCK_ID            1u      /* NvM block ID for configuration */
#define HEATING_CONTROL_CONFIG_SIZE             32u     /* Configuration data size (bytes) */

/* Communication parameters */
#define HEATING_CONTROL_LIN_TIMEOUT             1000u   /* LIN signal timeout (ms) */
#define HEATING_CONTROL_PASSENGER_TIMEOUT       5000u   /* Passenger presence timeout (ms) */

/* Diagnostic parameters */
#define HEATING_CONTROL_MAX_FAULT_COUNT         255u    /* Maximum fault count */

/* UDS diagnostic data identifiers */
#define HEATING_CONTROL_DID_CONFIG              0x1001u /* Configuration data identifier */
#define HEATING_CONTROL_DID_STATUS              0x1002u /* Status data identifier */
#define HEATING_CONTROL_DID_DIAGNOSTIC          0x1003u /* Diagnostic data identifier */
#define HEATING_CONTROL_DID_PID_PARAMS          0x1004u /* PID parameters identifier */
#define HEATING_CONTROL_DID_LEVEL_TEMPS         0x1005u /* Level temperatures identifier */
```

#### 4.1.7 Interface Description

##### ******* Initialization Functions
- **HeatingControl_Init**: Initializes the heating control module reads NvM configuration, and transitions to HMC_STATE_OFF if no faults detected
- **HeatingControl_LoadConfiguration**: Loads configuration parameters from NvM including last heating level, PID parameters, and system settings
- **HeatingControl_SaveConfiguration**: Saves current configuration parameters to NvM before ECU shutdown

##### ******* Heating Level Control Functions
- **HeatingControl_SetHeatingLevel**: Sets the heating level (OFF, LEVEL_1 to LEVEL_5) and corresponding target temperature
- **HeatingControl_GetHeatingLevel**: Retrieves the current active heating level
- **HeatingControl_ProcessHeatingRequest**: Processes LIN bus heating requests (level and active/inactive commands) and manages state transitions

##### ******* Temperature Monitoring Functions
- **HeatingControl_GetCurrentTemperature**: Reads current temperature from ADC interface via. RET with data validity status
- **HeatingControl_GetTargetTemperature**: Retrieves the target temperature for current heating level

##### 4.1.7.4 System State and Status Functions
- **HeatingControl_GetSystemState**: Returns current state machine state (INIT, OFF, ACTIVE, FAULT)
- **HeatingControl_GetSystemStatus**: Returns system status for LIN communication (Off, Active, Fault)
- **HeatingControl_GetFaultStatus**: Retrieves active fault flags and error codes

##### 4.1.7.5 Timer and Safety Functions
- **HeatingControl_GetRemainingTime**: Returns remaining heating time in 30-minute timer mode
- **HeatingControl_ResetTimer**: Resets the heating timer (used when passenger presence detected)

##### 4.1.7.6 Configuration and Calibration Functions
- **HeatingControl_SetPIDParameters**: Sets PID control parameters (Kp, Ki, Kd)
- **HeatingControl_GetPIDParameters**: Retrieves current PID control parameters
- **HeatingControl_SetLevelTemperatures**: Sets target temperatures for each heating level

##### 4.1.7.7 Main Processing Function
- **HeatingControl_MainFunction**: Main cyclic function (100ms) that implements:
  - State machine logic
  - PID temperature control algorithm
  - PWM duty cycle calculation and output
  - Timer management (30-minute auto shutoff)
  - Fault monitoring and response
  - LIN communication signal processing

#### 4.1.8 RTE Port Mapping

##### 4.1.8.1 Required Ports (Inputs)
```c
/* Temperature sensor input from Safety Monitor SWC */
R-Port: RP_TemperatureSensor
Interface: SR_TemperatureData
Data Element: temperatureValue, temperatureStatus

/* Safety monitor input from Safety Monitor SWC */
R-Port: RP_SafetyMonitor  
Interface: SR_SafetyStatus
Data Element: safetyFaultActive, temperatureOverLimit, heatingMatOpenCircuit, heatingMatShortCircuit, voltageOverLimit, voltageUnderLimit, safetyMonitorState, safetyDataStatus

/* LIN communication input from Communication SWC - Individual signal ports */
R-Port: RP_HeatingLevelRequest
Interface: SR_HeatingLevelRequest
Data Element: heatingLevelRequest

R-Port: RP_HeatingActiveRequest
Interface: SR_HeatingActiveRequest
Data Element: heatingActiveRequest

R-Port: RP_OccupantStatus
Interface: SR_OccupantStatus
Data Element: passengerPresent, signalStatus
```

##### 4.1.8.2 Provided Ports (Outputs)
```c
/* PWM control output to PWM Driver */
P-Port: PP_PWMControl
Interface: SR_PWMControl
Data Element: pwmDutyCycle, pwmEnable

/* LIN communication output to Communication SWC - Individual signal ports */
P-Port: PP_HeatingMatStatus
Interface: SR_HeatingMatStatus
Data Element: heatingMatStatus

P-Port: PP_HeatingLevelResponse
Interface: SR_HeatingLevelResponse
Data Element: heatingLevelRes

P-Port: PP_CurrentTemperature
Interface: SR_CurrentTemperature
Data Element: currentTemperature

P-Port: PP_RemainingTime
Interface: SR_RemainingTime
Data Element: remainingTime
```
