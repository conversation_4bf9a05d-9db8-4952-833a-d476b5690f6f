################################################################################
# Automatically-generated file. Do not edit!
################################################################################

-include ../makefile.init

RM := rm -rf

# All of the sources participating in the build are defined here
-include sources.mk
-include 03_BSW/ZhiXinSDK/StdDriver/Src/subdir.mk
-include 03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/subdir.mk
-include 03_BSW/System/03_MCAL/subdir.mk
-include 03_BSW/System/02_HAL/subdir.mk
-include 03_BSW/System/01_Service/subdir.mk
-include 03_BSW/STAR/subdir.mk
-include 03_BSW/Memory/03_MCAL/subdir.mk
-include 03_BSW/Memory/02_HAL/subdir.mk
-include 03_BSW/Memory/01_Service/subdir.mk
-include 03_BSW/IO/03_MCAL/subdir.mk
-include 03_BSW/IO/02_HAL/subdir.mk
-include 02_RTE/subdir.mk
-include 01_SWC/SafetyMonitor/subdir.mk
-include 01_SWC/HeatingMatControl/subdir.mk
-include .metadata/.plugins/org.eclipse.cdt.make.core/subdir.mk
-include subdir.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(SX_DEPS)),)
-include $(SX_DEPS)
endif
ifneq ($(strip $(ASM_DEPS)),)
-include $(ASM_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
endif

-include ../makefile.defs

OPTIONAL_TOOL_DEPS := \
$(wildcard ../makefile.defs) \
$(wildcard ../makefile.init) \
$(wildcard ../makefile.targets) \


BUILD_ARTIFACT_NAME := 48V code
BUILD_ARTIFACT_EXTENSION := elf
BUILD_ARTIFACT_PREFIX :=
BUILD_ARTIFACT := $(BUILD_ARTIFACT_PREFIX)$(BUILD_ARTIFACT_NAME)$(if $(BUILD_ARTIFACT_EXTENSION),.$(BUILD_ARTIFACT_EXTENSION),)

# Add inputs and outputs from these tool invocations to the build variables 
SECONDARY_FLASH += \
48V\ code.hex \

SECONDARY_SIZE += \
48V\ code.siz \


# All Target
all: main-build

# Main-build Target
main-build: 48V\ code.elf secondary-outputs

# Tool invocations
48V\ code.elf: $(OBJS) $(USER_OBJS) makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Building target: $@'
	@echo 'Invoking: GNU Arm Cross C Linker'
	arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -T "D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M\GCC\Z20K116M_flash.ld" -Xlinker --gc-sections -Wl,-Map,"48V code.map" --specs=nosys.specs -o "48V code.elf" $(OBJS) $(USER_OBJS) $(LIBS)
	@echo 'Finished building target: $@'
	@echo ' '

48V\ code.hex: 48V\ code.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Invoking: GNU Arm Cross Create Flash Image'
	arm-none-eabi-objcopy -O ihex "48V code.elf"  "48V code.hex"
	@echo 'Finished building: $@'
	@echo ' '

48V\ code.siz: 48V\ code.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Invoking: GNU Arm Cross Print Size'
	arm-none-eabi-size --format=berkeley "48V code.elf"
	@echo 'Finished building: $@'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(SX_DEPS)$(OBJS)$(SECONDARY_FLASH)$(SECONDARY_SIZE)$(ASM_DEPS)$(S_UPPER_DEPS)$(C_DEPS) "48V code.elf"
	-@echo ' '

secondary-outputs: $(SECONDARY_FLASH) $(SECONDARY_SIZE)

.PHONY: all clean dependents main-build

-include ../makefile.targets
