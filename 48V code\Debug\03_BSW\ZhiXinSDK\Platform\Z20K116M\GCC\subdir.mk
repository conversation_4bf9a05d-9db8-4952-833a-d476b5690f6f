################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
S_UPPER_SRCS += \
../03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.S 

OBJS += \
./03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.o 

S_UPPER_DEPS += \
./03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/Z20K116M_startup.d 


# Each subdirectory must supply rules for building sources it contributes
03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/%.o: ../03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/%.S 03_BSW/ZhiXinSDK/Platform/Z20K116M/GCC/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU Arm Cross Assembler'
	arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -x assembler-with-cpp -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


