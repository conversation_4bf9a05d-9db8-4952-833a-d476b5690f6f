################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../03_BSW/IO/02_HAL/AdcIf.c \
../03_BSW/IO/02_HAL/DioIf.c \
../03_BSW/IO/02_HAL/PwmIf.c 

OBJS += \
./03_BSW/IO/02_HAL/AdcIf.o \
./03_BSW/IO/02_HAL/DioIf.o \
./03_BSW/IO/02_HAL/PwmIf.o 

C_DEPS += \
./03_BSW/IO/02_HAL/AdcIf.d \
./03_BSW/IO/02_HAL/DioIf.d \
./03_BSW/IO/02_HAL/PwmIf.d 


# Each subdirectory must supply rules for building sources it contributes
03_BSW/IO/02_HAL/%.o: ../03_BSW/IO/02_HAL/%.c 03_BSW/IO/02_HAL/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU Arm Cross C Compiler'
	arm-none-eabi-gcc -mcpu=cortex-m0plus -mthumb -mlittle-endian -O0 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -g3 -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Core" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\Platform\Z20K116M" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Inc" -I"D:\NN\Project\48V\48V code\03_BSW\ZhiXinSDK\StdDriver\Src" -I"D:\NN\Project\48V\48V code\03_BSW\IO\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\IO\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\Memory\03_MCAL" -I"D:\NN\Project\48V\48V code\03_BSW\STAR" -I"D:\NN\Project\48V\48V code\03_BSW\System\01_Service" -I"D:\NN\Project\48V\48V code\03_BSW\System\02_HAL" -I"D:\NN\Project\48V\48V code\03_BSW\System\03_MCAL" -I"D:\NN\Project\48V\48V code\01_SWC\HeatingMatControl" -I"D:\NN\Project\48V\48V code\01_SWC\SafetyMonitor" -I"D:\NN\Project\48V\48V code\02_RTE" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


