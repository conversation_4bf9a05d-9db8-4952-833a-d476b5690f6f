/*******************************************************************************
** Copyright (c) 2025 FAURECIA
**
** This software is the property of Faurecia.
** It can not be used or duplicated without Faurecia authorization.
** -----------------------------------------------------------------------------
** File Name   : HeatingMatControl.c
** Module Name : Heating Mat Control SWC
** -----------------------------------------------------------------------------
**
** Description : Source file for Heating Mat Control Software Component
** This file contains the implementation of the Heating Mat Control SWC
** providing heating level control, temperature regulation, and safety
** monitoring functionality for the 48V Smart Heating Mat system.
** -----------------------------------------------------------------------------
**
** Documentation reference : 48V Smart Heating Mat Software Requirements.md
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** V01.00  08/26/2025
** - Baseline Created
**
*******************************************************************************/

/*************************** Inclusion files **********************************/
#include "HeatingMatControl.h"

/* Temporary implementation until Safety Monitor SWC is fully integrated */

/********************** Component configuration *******************************/

/********************** Declaration of local functions ************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_StateMachine(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_ProcessInputs(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_ProcessOutputs(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_PIDControl(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_TimerManagement(void);

STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_ReadSafetyStatus(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleInitializationMode(void);
STATIC FUNC(HeatingControl_FaultType, HEATINGCONTROL_CODE) HeatingControl_MapSafetyFaults(VAR(uint16, HEATINGCONTROL_VAR) safetyFaults);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleOffMode(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleActiveMode(void);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleFaultMode(void);
STATIC FUNC(sint16, HEATINGCONTROL_CODE) HeatingControl_GetTargetTempForLevel(VAR(HeatingControl_LevelType, HEATINGCONTROL_VAR) level);
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_SetPWMOutput(VAR(uint8, HEATINGCONTROL_VAR) dutyCycle);
STATIC FUNC(boolean, HEATINGCONTROL_CODE) HeatingControl_ValidateConfiguration(void);

/******************* Declaration of local variables ***************************/

/* Runtime data structure */
VAR(HeatingControl_RuntimeDataType, HEATINGCONTROL_VAR) HeatingControl_RuntimeData = {
    HMC_STATE_INIT,                          /* currentState */
    HEATING_LEVEL_OFF,                       /* currentLevel */
    HEATINGCONTROL_INIT_TEMPERATURE,         /* currentTemperature */
    HEATINGCONTROL_INIT_TARGET_TEMP,         /* targetTemperature */
    HEATINGCONTROL_INIT_PWM_DUTY,            /* currentPWMDutyCycle */
    HEATINGCONTROL_INIT_TIMER,               /* heatingTimer */
    FALSE,                                   /* heatingActive */
    HEATING_CONTROL_FAULT_NONE               /* activeFaults */
};

/* Configuration data structure */
VAR(HeatingControl_ConfigType, HEATINGCONTROL_VAR) HeatingControl_Config = {
    TRUE,                                    /* heatingFunctionEnabled */
    HEATING_TIMER_30MIN,                     /* timerMode */
    HEATINGCONTROL_MAX_HEATING_TIME_SEC,     /* maxHeatingTime */
    {                                        /* pidParams */
        HEATINGCONTROL_PID_KP_DEFAULT,       /* pidKp */
        HEATINGCONTROL_PID_KI_DEFAULT,       /* pidKi */
        HEATINGCONTROL_PID_KD_DEFAULT        /* pidKd */
    },
    {                                        /* levelTemps */
        HEATINGCONTROL_DEFAULT_TEMP_LEVEL_1, /* level1Temp */
        HEATINGCONTROL_DEFAULT_TEMP_LEVEL_2, /* level2Temp */
        HEATINGCONTROL_DEFAULT_TEMP_LEVEL_3, /* level3Temp */
        HEATINGCONTROL_DEFAULT_TEMP_LEVEL_4, /* level4Temp */
        HEATINGCONTROL_DEFAULT_TEMP_LEVEL_5  /* level5Temp */
    }
};

/* PID control data structure */
VAR(HeatingControl_PIDDataType, HEATINGCONTROL_VAR) HeatingControl_PIDData = {
    HEATINGCONTROL_INIT_PID_LAST_ERROR,      /* previousError */
    HEATINGCONTROL_INIT_PID_INTEGRAL,        /* integralSum */
    HEATINGCONTROL_INIT_DERIVATIVE_ERROR,    /* derivativeError */
    HEATINGCONTROL_INIT_LAST_UPDATE_TIME     /* lastUpdateTime */
};

/* Additional runtime variables not in the standard structure */
STATIC VAR(HeatingControl_LevelType, HEATINGCONTROL_VAR) HeatingControl_RequestedLevel = HEATING_LEVEL_OFF;
STATIC VAR(boolean, HEATINGCONTROL_VAR) HeatingControl_OccupantPresent = FALSE;
STATIC VAR(uint8, HEATINGCONTROL_VAR) HeatingControl_TimerCycleCounter = HEATINGCONTROL_INIT_CYCLE_COUNTER;
STATIC VAR(boolean, HEATINGCONTROL_VAR) HeatingControl_NvMConfigLoaded = HEATINGCONTROL_INIT_NVM_CONFIG_FLAG;
STATIC VAR(uint8, HEATINGCONTROL_VAR) HeatingControl_SafetyMonitorState = SAFETY_STATE_INIT;

/********************** Declaration of local constants *************************/

/******************** Declaration of exported variables ***********************/

/****************** Declaration of exported constant **************************/

/********************** Function implementations ******************************/

/**************************************************************************************************
** Function name    : HeatingControl_Init
** Description      : Initialize the Heating Mat Control module
** Parameter index  : None
** Return value     : Std_ReturnType - E_OK/E_NOT_OK
** Remarks          : REQ_HMC_FUNC_001 - Initialize module data structures
**************************************************************************************************/
FUNC(Std_ReturnType, HEATINGCONTROL_CODE) HeatingControl_Init(void)
{
    VAR(Std_ReturnType, HEATINGCONTROL_VAR) HeatingControl_iReturnValue = E_OK;

    /* Initialize runtime data to default values */
    HeatingControl_RuntimeData.currentState = HMC_STATE_INIT;
    HeatingControl_RuntimeData.currentLevel = HEATING_LEVEL_OFF;
    HeatingControl_RuntimeData.currentTemperature = HEATINGCONTROL_INIT_TEMPERATURE;
    HeatingControl_RuntimeData.targetTemperature = HEATINGCONTROL_INIT_TARGET_TEMP;
    HeatingControl_RuntimeData.currentPWMDutyCycle = HEATINGCONTROL_INIT_PWM_DUTY;
    HeatingControl_RuntimeData.heatingTimer = HEATINGCONTROL_INIT_TIMER;
    HeatingControl_RuntimeData.heatingActive = FALSE;
    HeatingControl_RuntimeData.activeFaults = HEATING_CONTROL_FAULT_NONE;

    /* Initialize PID data */
    HeatingControl_PIDData.previousError = HEATINGCONTROL_INIT_PID_LAST_ERROR;
    HeatingControl_PIDData.integralSum = HEATINGCONTROL_INIT_PID_INTEGRAL;
    HeatingControl_PIDData.derivativeError = HEATINGCONTROL_INIT_DERIVATIVE_ERROR;
    HeatingControl_PIDData.lastUpdateTime = HEATINGCONTROL_INIT_LAST_UPDATE_TIME;

    /* Initialize additional runtime variables */
    HeatingControl_RequestedLevel = HEATING_LEVEL_OFF;
    HeatingControl_OccupantPresent = FALSE;
    HeatingControl_TimerCycleCounter = HEATINGCONTROL_INIT_CYCLE_COUNTER;
    HeatingControl_NvMConfigLoaded = HEATINGCONTROL_INIT_NVM_CONFIG_FLAG;
    HeatingControl_SafetyMonitorState = SAFETY_STATE_INIT;

    /* Initialize PWM output to safe state */
    HeatingControl_SetPWMOutput(HEATINGCONTROL_INIT_PWM_DUTY);

    return HeatingControl_iReturnValue;
}

/**************************************************************************************************
** Function name    : HeatingControl_LoadConfiguration
** Description      : Load configuration from NvM
** Parameter index  : None
** Return value     : Std_ReturnType - E_OK/E_NOT_OK
** Remarks          : REQ_HMC_FUNC_002 - Load configuration from non-volatile memory
**************************************************************************************************/
FUNC(Std_ReturnType, HEATINGCONTROL_CODE) HeatingControl_LoadConfiguration(void)
{
    VAR(Std_ReturnType, HEATINGCONTROL_VAR) HeatingControl_iReturnValue = E_OK;
    VAR(uint8, HEATINGCONTROL_VAR) HeatingControl_iNvMData[HEATINGCONTROL_NVM_BLOCK_SIZE];

    /* Read configuration from NvM */
    HeatingControl_iReturnValue = Rte_Call_NvMService_ReadBlock(
        HEATINGCONTROL_NVM_BLOCK_CONFIG,
        HeatingControl_iNvMData);

    if (HeatingControl_iReturnValue == E_OK)
    {
        /* Validate and load heating function enable flag */
        if (HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_HEATING_ENABLE] <= HEATINGCONTROL_BOOLEAN_TRUE_VALUE)
        {
            HeatingControl_Config.heatingFunctionEnabled = (boolean)HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_HEATING_ENABLE];
        }
        else
        {
            /* Invalid value - use default */
            HeatingControl_Config.heatingFunctionEnabled = TRUE;
        }

        /* Validate and load timer mode */
        if (HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_TIMER_MODE] <= HEATING_TIMER_PASSENGER_BASED)
        {
            HeatingControl_Config.timerMode = (HeatingControl_TimerModeType)HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_TIMER_MODE];
        }
        else
        {
            /* Invalid value - use default */
            HeatingControl_Config.timerMode = HEATING_TIMER_30MIN;
        }

        /* Validate and load heating level */
        if (HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL] <= HEATING_LEVEL_5)
        {
            HeatingControl_RequestedLevel = (HeatingControl_LevelType)HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL];
        }
        else
        {
            /* Invalid value - use default */
            HeatingControl_RequestedLevel = HEATING_LEVEL_OFF;
        }

        /* Load PID parameters */
        HeatingControl_Config.pidParams.pidKp = (uint16)((HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KP_HIGH] << HEATINGCONTROL_BYTE_SHIFT_8) | HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KP_LOW]);
        HeatingControl_Config.pidParams.pidKi = (uint16)((HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KI_HIGH] << HEATINGCONTROL_BYTE_SHIFT_8) | HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KI_LOW]);
        HeatingControl_Config.pidParams.pidKd = (uint16)((HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KD_HIGH] << HEATINGCONTROL_BYTE_SHIFT_8) | HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KD_LOW]);

        /* Validate PID parameters */
        if (HeatingControl_Config.pidParams.pidKp > HEATINGCONTROL_PID_MAX_VALUE)
        {
            HeatingControl_Config.pidParams.pidKp = HEATINGCONTROL_PID_KP_DEFAULT;
        }
        if (HeatingControl_Config.pidParams.pidKi > HEATINGCONTROL_PID_MAX_VALUE)
        {
            HeatingControl_Config.pidParams.pidKi = HEATINGCONTROL_PID_KI_DEFAULT;
        }
        if (HeatingControl_Config.pidParams.pidKd > HEATINGCONTROL_PID_MAX_VALUE)
        {
            HeatingControl_Config.pidParams.pidKd = HEATINGCONTROL_PID_KD_DEFAULT;
        }

        /* Load level temperatures */
        HeatingControl_Config.levelTemps.level1Temp = HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL1_TEMP];
        HeatingControl_Config.levelTemps.level2Temp = HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL2_TEMP];
        HeatingControl_Config.levelTemps.level3Temp = HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL3_TEMP];
        HeatingControl_Config.levelTemps.level4Temp = HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL4_TEMP];
        HeatingControl_Config.levelTemps.level5Temp = HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL5_TEMP];

        /* Validate level temperatures */
        if ((HeatingControl_Config.levelTemps.level1Temp < HEATINGCONTROL_MIN_LEVEL_TEMP) ||
            (HeatingControl_Config.levelTemps.level1Temp > HEATINGCONTROL_MAX_LEVEL_TEMP))
        {
            HeatingControl_Config.levelTemps.level1Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_1;
        }
        if ((HeatingControl_Config.levelTemps.level2Temp < HEATINGCONTROL_MIN_LEVEL_TEMP) ||
            (HeatingControl_Config.levelTemps.level2Temp > HEATINGCONTROL_MAX_LEVEL_TEMP))
        {
            HeatingControl_Config.levelTemps.level2Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_2;
        }
        if ((HeatingControl_Config.levelTemps.level3Temp < HEATINGCONTROL_MIN_LEVEL_TEMP) ||
            (HeatingControl_Config.levelTemps.level3Temp > HEATINGCONTROL_MAX_LEVEL_TEMP))
        {
            HeatingControl_Config.levelTemps.level3Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_3;
        }
        if ((HeatingControl_Config.levelTemps.level4Temp < HEATINGCONTROL_MIN_LEVEL_TEMP) ||
            (HeatingControl_Config.levelTemps.level4Temp > HEATINGCONTROL_MAX_LEVEL_TEMP))
        {
            HeatingControl_Config.levelTemps.level4Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_4;
        }
        if ((HeatingControl_Config.levelTemps.level5Temp < HEATINGCONTROL_MIN_LEVEL_TEMP) ||
            (HeatingControl_Config.levelTemps.level5Temp > HEATINGCONTROL_MAX_LEVEL_TEMP))
        {
            HeatingControl_Config.levelTemps.level5Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_5;
        }

        /* Validate configuration consistency */
        if (HeatingControl_ValidateConfiguration() == FALSE)
        {
            HeatingControl_iReturnValue = E_NOT_OK;
        }
    }
    else
    {
        /* NvM read failed - use default configuration */
        HeatingControl_Config.heatingFunctionEnabled = TRUE;
        HeatingControl_Config.timerMode = HEATING_TIMER_30MIN;
        HeatingControl_Config.pidParams.pidKp = HEATINGCONTROL_PID_KP_DEFAULT;
        HeatingControl_Config.pidParams.pidKi = HEATINGCONTROL_PID_KI_DEFAULT;
        HeatingControl_Config.pidParams.pidKd = HEATINGCONTROL_PID_KD_DEFAULT;
        HeatingControl_Config.levelTemps.level1Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_1;
        HeatingControl_Config.levelTemps.level2Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_2;
        HeatingControl_Config.levelTemps.level3Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_3;
        HeatingControl_Config.levelTemps.level4Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_4;
        HeatingControl_Config.levelTemps.level5Temp = HEATINGCONTROL_DEFAULT_TEMP_LEVEL_5;
        HeatingControl_RequestedLevel = HEATING_LEVEL_OFF;
    }

    return HeatingControl_iReturnValue;
}

/**************************************************************************************************
** Function name    : HeatingControl_SaveConfiguration
** Description      : Save configuration to NvM
** Parameter index  : None
** Return value     : Std_ReturnType - E_OK/E_NOT_OK
** Remarks          : REQ_HMC_FUNC_003 - Save configuration to non-volatile memory
**************************************************************************************************/
FUNC(Std_ReturnType, HEATINGCONTROL_CODE) HeatingControl_SaveConfiguration(void)
{
    VAR(Std_ReturnType, HEATINGCONTROL_VAR) HeatingControl_iReturnValue = E_OK;
    VAR(uint8, HEATINGCONTROL_VAR) HeatingControl_iNvMData[HEATINGCONTROL_NVM_BLOCK_SIZE];

    /* Prepare NvM data */
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_HEATING_ENABLE] = (uint8)HeatingControl_Config.heatingFunctionEnabled;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_TIMER_MODE] = (uint8)HeatingControl_Config.timerMode;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL] = (uint8)HeatingControl_RuntimeData.currentLevel;

    /* Pack PID parameters */
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KP_HIGH] = (uint8)(HeatingControl_Config.pidParams.pidKp >> HEATINGCONTROL_BYTE_SHIFT_8);
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KP_LOW] = (uint8)(HeatingControl_Config.pidParams.pidKp & HEATINGCONTROL_BYTE_MASK_LOW);
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KI_HIGH] = (uint8)(HeatingControl_Config.pidParams.pidKi >> HEATINGCONTROL_BYTE_SHIFT_8);
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KI_LOW] = (uint8)(HeatingControl_Config.pidParams.pidKi & HEATINGCONTROL_BYTE_MASK_LOW);
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KD_HIGH] = (uint8)(HeatingControl_Config.pidParams.pidKd >> HEATINGCONTROL_BYTE_SHIFT_8);
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_PID_KD_LOW] = (uint8)(HeatingControl_Config.pidParams.pidKd & HEATINGCONTROL_BYTE_MASK_LOW);

    /* Pack level temperatures */
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL1_TEMP] = HeatingControl_Config.levelTemps.level1Temp;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL2_TEMP] = HeatingControl_Config.levelTemps.level2Temp;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL3_TEMP] = HeatingControl_Config.levelTemps.level3Temp;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL4_TEMP] = HeatingControl_Config.levelTemps.level4Temp;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_LEVEL5_TEMP] = HeatingControl_Config.levelTemps.level5Temp;

    /* Set reserved bytes to zero */
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_RESERVED_1] = HEATINGCONTROL_RESERVED_BYTE_VALUE;
    HeatingControl_iNvMData[HEATINGCONTROL_NVM_IDX_RESERVED_2] = HEATINGCONTROL_RESERVED_BYTE_VALUE;

    /* Write configuration to NvM */
    HeatingControl_iReturnValue = Rte_Call_NvMService_WriteBlock(HEATINGCONTROL_NVM_BLOCK_CONFIG, HeatingControl_iNvMData);

    return HeatingControl_iReturnValue;
}

/**************************************************************************************************
** Function name    : HeatingControl_MainFunction
** Description      : Main function called every 100ms
** Parameter index  : None
** Return value     : None
** Remarks          : REQ_HMC_FUNC_026 - 100ms execution cycle with state-based processing
**************************************************************************************************/
FUNC(void, HEATINGCONTROL_CODE) HeatingControl_MainFunction(void)
{
    /* Execute state-specific processing based on current state */
    switch (HeatingControl_RuntimeData.currentState)
    {
        case HMC_STATE_INIT:
            /* Handle initialization mode */
            HeatingControl_HandleInitializationMode();
            break;

        case HMC_STATE_OFF:
            /* Handle off mode */
            HeatingControl_HandleOffMode();
            break;

        case HMC_STATE_ACTIVE:
            /* Handle active heating mode */
            HeatingControl_HandleActiveMode();
            break;

        case HMC_STATE_FAULT:
            /* Handle fault mode */
            HeatingControl_HandleFaultMode();
            break;

        default:
            /* Invalid state - transition to fault mode */
            HeatingControl_RuntimeData.currentState = HMC_STATE_FAULT;
            /* Set a general fault for invalid state - use temperature fault as fallback */
            HeatingControl_RuntimeData.activeFaults |= HEATING_CONTROL_FAULT_TEMPERATURE_OVER_LIMIT;
            break;
    }
}

/**************************************************************************************************
** Function name    : HeatingControl_HandleInitializationMode
** Description      : Handle initialization state processing
** Parameter index  : None
** Return value     : None
** Remarks          : Load NvM configuration and monitor SafetyMonitor state for transition
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleInitializationMode(void)
{
    VAR(Std_ReturnType, HEATINGCONTROL_VAR) HeatingControl_iNvMResult;
    VAR(Rte_SafetyFaultDataType, HEATINGCONTROL_VAR) HeatingControl_iFaultData;

    /* Load configuration from NvM if not already loaded */
    if (HeatingControl_NvMConfigLoaded == FALSE)
    {
        HeatingControl_iNvMResult = HeatingControl_LoadConfiguration();

        if (HeatingControl_iNvMResult == E_OK)
        {
            HeatingControl_NvMConfigLoaded = TRUE;
        }
        else
        {
            /* NvM load failed - use defaults, no specific fault for NvM access */
            /* Continue with default configuration */
        }
    }

    /* Read safety status from Safety Monitor */
    HeatingControl_ReadSafetyStatus();

    /* State transition logic based on SafetyMonitor state */
    if (HeatingControl_SafetyMonitorState == SAFETY_STATE_INIT)
    {
        /* SafetyMonitor is still in INIT state - remain in INIT state */
        /* No state change needed */
    }
    else if ((HeatingControl_SafetyMonitorState == SAFETY_STATE_NORMAL) &&
             (HeatingControl_NvMConfigLoaded == TRUE))
    {
        /* SafetyMonitor is in NORMAL state and NvM configuration loaded - transition to OFF state */
        HeatingControl_RuntimeData.currentState = HMC_STATE_OFF;
    }
    else if (HeatingControl_SafetyMonitorState == SAFETY_STATE_FAULT)
    {
        /* SafetyMonitor is in FAULT state - get fault data via RTE and sync fault information */
        if (Rte_Read_SafetyFaultData(&HeatingControl_iFaultData) == E_OK)
        {
            if (HeatingControl_iFaultData.dataStatus == RTE_TYPE_DATA_VALID)
            {
                /* Map SafetyMonitor faults to HeatingControl faults */
                HeatingControl_RuntimeData.activeFaults = HeatingControl_MapSafetyFaults(HeatingControl_iFaultData.activeFaults);
            }
        }

        /* Transition to FAULT state */
        HeatingControl_RuntimeData.currentState = HMC_STATE_FAULT;
    }
    else
    {
        /* Other conditions - remain in INIT state for next cycle */
        /* This covers cases where SafetyMonitor is NORMAL but NvM is not loaded yet */
    }
}

/**************************************************************************************************
** Function name    : HeatingControl_HandleOffMode
** Description      : Handle OFF state processing
** Parameter index  : None
** Return value     : None
** Remarks          : Process inputs and check for heating requests
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleOffMode(void)
{
    /* Process input signals from RTE */
    HeatingControl_ProcessInputs();

    /* Execute state machine logic */
    HeatingControl_StateMachine();

    /* Process output signals to RTE */
    HeatingControl_ProcessOutputs();
}

/**************************************************************************************************
** Function name    : HeatingControl_HandleActiveMode
** Description      : Handle ACTIVE state processing
** Parameter index  : None
** Return value     : None
** Remarks          : Full control loop with PID control and timer management
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleActiveMode(void)
{
    /* Process input signals from RTE */
    HeatingControl_ProcessInputs();

    /* Execute state machine logic */
    HeatingControl_StateMachine();

    /* Execute PID control algorithm */
    HeatingControl_PIDControl();

    /* Manage heating timer */
    HeatingControl_TimerManagement();

    /* Process output signals to RTE */
    HeatingControl_ProcessOutputs();
}

/**************************************************************************************************
** Function name    : HeatingControl_HandleFaultMode
** Description      : Handle FAULT state processing
** Parameter index  : None
** Return value     : None
** Remarks          : Minimal processing in fault state, monitor for fault recovery
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_HandleFaultMode(void)
{
    /* Ensure PWM output is disabled in fault state */
    HeatingControl_SetPWMOutput(HEATINGCONTROL_INIT_PWM_DUTY);

    /* Process input signals from RTE (minimal) */
    HeatingControl_ProcessInputs();

    /* Execute state machine logic for fault recovery */
    HeatingControl_StateMachine();

    /* Process output signals to RTE */
    HeatingControl_ProcessOutputs();
}

/**************************************************************************************************
** Function name    : HeatingControl_ProcessInputs
** Description      : Process input signals from RTE
** Parameter index  : None
** Return value     : None
** Remarks          : REQ_HMC_COMM_001 - Read input signals from RTE
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_ProcessInputs(void)
{
    VAR(Rte_TemperatureDataType, HEATINGCONTROL_VAR) HeatingControl_iTempData;
    VAR(Rte_LIN_HeatingLevelRequestType, HEATINGCONTROL_VAR) HeatingControl_iLevelRequest;
    VAR(Rte_LIN_HeatingActiveRequestType, HEATINGCONTROL_VAR) HeatingControl_iActiveRequest;
    VAR(Rte_LIN_OccupantStatusType, HEATINGCONTROL_VAR) HeatingControl_iOccupantStatus;
    VAR(Rte_SafetyFaultDataType, HEATINGCONTROL_VAR) HeatingControl_iFaultData;

    /* Read safety status from Safety Monitor */
    HeatingControl_ReadSafetyStatus();

    /* Sync fault information from SafetyMonitor */
    if (HeatingControl_SafetyMonitorState == SAFETY_STATE_FAULT)
    {
        if (Rte_Read_SafetyFaultData(&HeatingControl_iFaultData) == E_OK)
        {
            if (HeatingControl_iFaultData.dataStatus == RTE_TYPE_DATA_VALID)
            {
                /* Map SafetyMonitor faults to HeatingControl faults */
                HeatingControl_RuntimeData.activeFaults = HeatingControl_MapSafetyFaults(HeatingControl_iFaultData.activeFaults);
            }
        }
    }
    else if (HeatingControl_SafetyMonitorState == SAFETY_STATE_NORMAL)
    {
        /* SafetyMonitor is normal - clear safety-related faults */
        HeatingControl_RuntimeData.activeFaults &= ~(HEATING_CONTROL_FAULT_TEMPERATURE_OVER_LIMIT |
                                                      HEATING_CONTROL_FAULT_HEATING_MAT_OPEN |
                                                      HEATING_CONTROL_FAULT_HEATING_MAT_SHORT |
                                                      HEATING_CONTROL_FAULT_VOLTAGE_OVER_LIMIT |
                                                      HEATING_CONTROL_FAULT_VOLTAGE_UNDER_LIMIT);
    }

    /* Read heating level request from LIN */
    if (Rte_Read_HeatingLevelRequest(&HeatingControl_iLevelRequest) == E_OK)
    {
        if (HeatingControl_iLevelRequest.levelRequestStatus == RTE_TYPE_DATA_VALID)
        {
            /* Validate heating level request */
            if (HeatingControl_iLevelRequest.heatingLevelRequest <= HEATING_LEVEL_5)
            {
                HeatingControl_RequestedLevel = (HeatingControl_LevelType)HeatingControl_iLevelRequest.heatingLevelRequest;
            }
            else
            {
                /* Invalid level request - ignore, no fault set for invalid requests */
                /* Continue with current level */
            }
        }
        else
        {
            /* Level request data invalid - ignore, no fault set */
            /* Continue with current level */
        }
    }

    /* Read heating active request from LIN */
    if (Rte_Read_HeatingActiveRequest(&HeatingControl_iActiveRequest) == E_OK)
    {
        if (HeatingControl_iActiveRequest.activeRequestStatus == RTE_TYPE_DATA_VALID)
        {
            HeatingControl_RuntimeData.heatingActive = HeatingControl_iActiveRequest.heatingActiveRequest;
        }
    }

    /* Read occupant status from LIN */
    if (Rte_Read_OccupantStatus(&HeatingControl_iOccupantStatus) == E_OK)
    {
        if (HeatingControl_iOccupantStatus.occupantStatus_Status == RTE_TYPE_DATA_VALID)
        {
            HeatingControl_OccupantPresent = HeatingControl_iOccupantStatus.occupantStatus;
        }
    }
}

/**************************************************************************************************
** Function name    : HeatingControl_ReadSafetyStatus
** Description      : Read safety status from Safety Monitor SWC via RTE
** Parameter index  : None
** Return value     : None
** Remarks          : Interface to Safety Monitor for current state information
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_ReadSafetyStatus(void)
{
    VAR(Rte_SafetyStatusType, HEATINGCONTROL_VAR) HeatingControl_iSafetyData;

    /* Read safety monitor state via RTE */
    (void)Rte_Read_SafetyStatus(&HeatingControl_iSafetyData);

    /* Store SafetyMonitor current state */
    HeatingControl_SafetyMonitorState = HeatingControl_iSafetyData.safetyMonitorState;
}

/**************************************************************************************************
** Function name    : HeatingControl_StateMachine
** Description      : Execute state machine logic
** Parameter index  : None
** Return value     : None
** Remarks          : REQ_HMC_FUNC_010 - State machine implementation
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_StateMachine(void)
{
    VAR(HeatingControl_StateType, HEATINGCONTROL_VAR) HeatingControl_iNextState;

    HeatingControl_iNextState = HeatingControl_RuntimeData.currentState;

    switch (HeatingControl_RuntimeData.currentState)
    {
        case HMC_STATE_OFF:
            /* Check for heating activation request */
            if ((HeatingControl_RuntimeData.heatingActive == TRUE) &&
                (HeatingControl_RequestedLevel > HEATING_LEVEL_OFF) &&
                (HeatingControl_Config.heatingFunctionEnabled == TRUE))
            {
                HeatingControl_iNextState = HMC_STATE_ACTIVE;
            }
            else if (HeatingControl_RuntimeData.activeFaults != HEATING_CONTROL_FAULT_NONE)
            {
                HeatingControl_iNextState = HMC_STATE_FAULT;
            }
            break;

        case HMC_STATE_ACTIVE:
            /* Check for heating deactivation conditions */
            if ((HeatingControl_RuntimeData.heatingActive == FALSE) ||
                (HeatingControl_RequestedLevel == HEATING_LEVEL_OFF))
            {
                HeatingControl_iNextState = HMC_STATE_OFF;
            }
            /* Check timer expiration */
            else if ((HeatingControl_Config.timerMode == HEATING_TIMER_30MIN) &&
                     (HeatingControl_RuntimeData.heatingTimer >= HeatingControl_Config.maxHeatingTime))
            {
                HeatingControl_iNextState = HMC_STATE_OFF;
            }
            /* Check occupant-based timer */
            else if ((HeatingControl_Config.timerMode == HEATING_TIMER_PASSENGER_BASED) &&
                     (HeatingControl_OccupantPresent == FALSE))
            {
                HeatingControl_iNextState = HMC_STATE_OFF;
            }
            else if (HeatingControl_RuntimeData.activeFaults != HEATING_CONTROL_FAULT_NONE)
            {
                HeatingControl_iNextState = HMC_STATE_FAULT;
            }
            break;

        case HMC_STATE_FAULT:
            /* Check for fault recovery */
            if (HeatingControl_RuntimeData.activeFaults == HEATING_CONTROL_FAULT_NONE)
            {
                HeatingControl_iNextState = HMC_STATE_OFF;
            }
            break;

        default:
            HeatingControl_iNextState = HMC_STATE_FAULT;
            break;
    }

    /* Update state */
    HeatingControl_RuntimeData.currentState = HeatingControl_iNextState;
}

/**************************************************************************************************
** Function name    : HeatingControl_SetPWMOutput
** Description      : Set PWM output duty cycle
** Parameter index  : dutyCycle - PWM duty cycle (0-100)
** Return value     : None
** Remarks          : REQ_HMC_FUNC_040 - PWM output control
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_SetPWMOutput(VAR(uint8, HEATINGCONTROL_VAR) dutyCycle)
{
    VAR(Rte_PWMControlType, HEATINGCONTROL_VAR) HeatingControl_iPWMData;

    /* Update runtime data */
    HeatingControl_RuntimeData.currentPWMDutyCycle = dutyCycle;
 
    /* Prepare PWM output data */
    HeatingControl_iPWMData.pwmDutyCycle = dutyCycle;
    HeatingControl_iPWMData.pwmEnable = (dutyCycle > HEATINGCONTROL_INIT_PWM_DUTY) ? TRUE : FALSE;

    /* Send PWM control signal via RTE */
    (void)Rte_Write_PWMControl(&HeatingControl_iPWMData);
}

/**************************************************************************************************
** Function name    : HeatingControl_ValidateConfiguration
** Description      : Validate configuration data consistency
** Parameter index  : None
** Return value     : boolean - TRUE if valid, FALSE if invalid
** Remarks          : REQ_HMC_FUNC_050 - Configuration validation
**************************************************************************************************/
STATIC FUNC(boolean, HEATINGCONTROL_CODE) HeatingControl_ValidateConfiguration(void)
{
    VAR(boolean, HEATINGCONTROL_VAR) HeatingControl_iReturnValue = TRUE;

    /* Validate PID parameters */
    if ((HeatingControl_Config.pidParams.pidKp == HEATINGCONTROL_INIT_PWM_DUTY) ||
        (HeatingControl_Config.pidParams.pidKp > HEATINGCONTROL_PID_MAX_VALUE))
    {
        HeatingControl_iReturnValue = FALSE;
    }

    if (HeatingControl_Config.pidParams.pidKi > HEATINGCONTROL_PID_MAX_VALUE)
    {
        HeatingControl_iReturnValue = FALSE;
    }

    if (HeatingControl_Config.pidParams.pidKd > HEATINGCONTROL_PID_MAX_VALUE)
    {
        HeatingControl_iReturnValue = FALSE;
    }

    /* Validate level temperatures are in ascending order */
    if ((HeatingControl_Config.levelTemps.level1Temp >= HeatingControl_Config.levelTemps.level2Temp) ||
        (HeatingControl_Config.levelTemps.level2Temp >= HeatingControl_Config.levelTemps.level3Temp) ||
        (HeatingControl_Config.levelTemps.level3Temp >= HeatingControl_Config.levelTemps.level4Temp) ||
        (HeatingControl_Config.levelTemps.level4Temp >= HeatingControl_Config.levelTemps.level5Temp))
    {
        HeatingControl_iReturnValue = FALSE;
    }

    /* Validate timer configuration */
    if (HeatingControl_Config.maxHeatingTime > HEATINGCONTROL_MAX_HEATING_TIME_LIMIT)
    {
        HeatingControl_iReturnValue = FALSE;
    }

    return HeatingControl_iReturnValue;
}

/**************************************************************************************************
** Function name    : HeatingControl_PIDControl
** Description      : Execute PID control algorithm
** Parameter index  : None
** Return value     : None
** Remarks          : REQ_HMC_FUNC_030 - PID temperature control
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_PIDControl(void)
{
    /* Only execute PID control in ACTIVE state */
    if (HeatingControl_RuntimeData.currentState == HMC_STATE_ACTIVE)
    {
        VAR(sint16, HEATINGCONTROL_VAR) HeatingControl_iError;
        VAR(sint16, HEATINGCONTROL_VAR) HeatingControl_iDerivative;
        VAR(sint32, HEATINGCONTROL_VAR) HeatingControl_iPIDOutput;
        VAR(uint8, HEATINGCONTROL_VAR) HeatingControl_iPWMDuty;

        /* Get target temperature for current level */
        HeatingControl_RuntimeData.targetTemperature = HeatingControl_GetTargetTempForLevel(HeatingControl_RequestedLevel);

        /* Calculate error */
        HeatingControl_iError = HeatingControl_RuntimeData.targetTemperature - HeatingControl_RuntimeData.currentTemperature;

        /* Only process if error is outside deadband */
        if ((HeatingControl_iError > HEATINGCONTROL_PID_ERROR_DEADBAND) ||
            (HeatingControl_iError < -HEATINGCONTROL_PID_ERROR_DEADBAND))
        {
            /* Update integral term */
            HeatingControl_PIDData.integralSum += HeatingControl_iError;

            /* Limit integral windup */
            if (HeatingControl_PIDData.integralSum > HEATINGCONTROL_PID_INTEGRAL_MAX)
            {
                HeatingControl_PIDData.integralSum = HEATINGCONTROL_PID_INTEGRAL_MAX;
            }
            else if (HeatingControl_PIDData.integralSum < HEATINGCONTROL_PID_INTEGRAL_MIN)
            {
                HeatingControl_PIDData.integralSum = HEATINGCONTROL_PID_INTEGRAL_MIN;
            }

            /* Calculate derivative term */
            HeatingControl_iDerivative = HeatingControl_iError - HeatingControl_PIDData.previousError;
            HeatingControl_PIDData.derivativeError = HeatingControl_iDerivative;

            /* Calculate PID output */
            HeatingControl_iPIDOutput =
                ((sint32)HeatingControl_Config.pidParams.pidKp * HeatingControl_iError) +
                ((sint32)HeatingControl_Config.pidParams.pidKi * HeatingControl_PIDData.integralSum / HEATINGCONTROL_PID_KI_SCALING) +
                ((sint32)HeatingControl_Config.pidParams.pidKd * HeatingControl_iDerivative);

            /* Scale PID output */
            HeatingControl_iPIDOutput = HeatingControl_iPIDOutput / HEATINGCONTROL_PID_SCALING_FACTOR;

            /* Limit output to valid PWM range */
            if (HeatingControl_iPIDOutput > HEATINGCONTROL_PID_OUTPUT_MAX)
            {
                HeatingControl_iPWMDuty = HEATINGCONTROL_PID_OUTPUT_MAX;
            }
            else if (HeatingControl_iPIDOutput < HEATINGCONTROL_PID_OUTPUT_MIN)
            {
                HeatingControl_iPWMDuty = HEATINGCONTROL_PID_OUTPUT_MIN;
            }
            else
            {
                HeatingControl_iPWMDuty = (uint8)HeatingControl_iPIDOutput;
            }

            /* Set PWM output */
            HeatingControl_SetPWMOutput(HeatingControl_iPWMDuty);

            /* Update previous error for next cycle */
            HeatingControl_PIDData.previousError = HeatingControl_iError;
        }
    }
    else
    {
        /* Not in ACTIVE state - disable PWM output */
        HeatingControl_SetPWMOutput(HEATINGCONTROL_INIT_PWM_DUTY);
    }
}

/**************************************************************************************************
** Function name    : HeatingControl_GetTargetTempForLevel
** Description      : Get target temperature for heating level
** Parameter index  : level - Heating level
** Return value     : sint16 - Target temperature
** Remarks          : REQ_HMC_FUNC_021 - Level temperature mapping
**************************************************************************************************/
STATIC FUNC(sint16, HEATINGCONTROL_CODE) HeatingControl_GetTargetTempForLevel(VAR(HeatingControl_LevelType, HEATINGCONTROL_VAR) level)
{
    VAR(sint16, HEATINGCONTROL_VAR) HeatingControl_iTargetTemp = HEATINGCONTROL_INIT_TARGET_TEMP;

    switch (level)
    {
        case HEATING_LEVEL_1:
            HeatingControl_iTargetTemp = (sint16)HeatingControl_Config.levelTemps.level1Temp;
            break;
        case HEATING_LEVEL_2:
            HeatingControl_iTargetTemp = (sint16)HeatingControl_Config.levelTemps.level2Temp;
            break;
        case HEATING_LEVEL_3:
            HeatingControl_iTargetTemp = (sint16)HeatingControl_Config.levelTemps.level3Temp;
            break;
        case HEATING_LEVEL_4:
            HeatingControl_iTargetTemp = (sint16)HeatingControl_Config.levelTemps.level4Temp;
            break;
        case HEATING_LEVEL_5:
            HeatingControl_iTargetTemp = (sint16)HeatingControl_Config.levelTemps.level5Temp;
            break;
        case HEATING_LEVEL_OFF:
        default:
            HeatingControl_iTargetTemp = HEATINGCONTROL_INIT_TARGET_TEMP;
            break;
    }

    return HeatingControl_iTargetTemp;
}

/**************************************************************************************************
** Function name    : HeatingControl_TimerManagement
** Description      : Manage heating timer
** Parameter index  : None
** Return value     : None
** Remarks          : REQ_HMC_FUNC_026 - Timer management
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_TimerManagement(void)
{
    /* Only manage timer in ACTIVE state */
    if (HeatingControl_RuntimeData.currentState == HMC_STATE_ACTIVE)
    {
        /* Increment timer counter */
        HeatingControl_TimerCycleCounter++;

        /* Convert cycles to seconds (100ms cycle time) */
        if (HeatingControl_TimerCycleCounter >= HEATINGCONTROL_TIMER_CYCLES_PER_SEC)
        {
            HeatingControl_RuntimeData.heatingTimer++;
            HeatingControl_TimerCycleCounter = HEATINGCONTROL_INIT_CYCLE_COUNTER;
        }
    }
    else
    {
        /* Reset timer when not in ACTIVE state */
        HeatingControl_RuntimeData.heatingTimer = HEATINGCONTROL_INIT_TIMER;
        HeatingControl_TimerCycleCounter = HEATINGCONTROL_INIT_CYCLE_COUNTER;
    }
}



/**************************************************************************************************
** Function name    : HeatingControl_ProcessOutputs
** Description      : Process output signals to RTE
** Parameter index  : None
** Return value     : None
** Remarks          : REQ_HMC_COMM_002 - Send output signals via RTE
**************************************************************************************************/
STATIC FUNC(void, HEATINGCONTROL_CODE) HeatingControl_ProcessOutputs(void)
{
    VAR(Rte_LIN_HeatingMatStatusType, HEATINGCONTROL_VAR) HeatingControl_iStatusData;
    VAR(Rte_LIN_HeatingLevelResponseType, HEATINGCONTROL_VAR) HeatingControl_iLevelResponse;
    VAR(Rte_LIN_CurrentTemperatureType, HEATINGCONTROL_VAR) HeatingControl_iTempResponse;
    VAR(Rte_LIN_RemainingTimeType, HEATINGCONTROL_VAR) HeatingControl_iTimeResponse;

    /* Prepare heating mat status */
    if (HeatingControl_RuntimeData.activeFaults != HEATING_CONTROL_FAULT_NONE)
    {
        HeatingControl_iStatusData.heatingMatStatus = RTE_TYPE_HEATING_STATUS_FAULT;
    }
    else if (HeatingControl_RuntimeData.currentState == HMC_STATE_ACTIVE)
    {
        HeatingControl_iStatusData.heatingMatStatus = RTE_TYPE_HEATING_STATUS_ACTIVE;
    }
    else
    {
        HeatingControl_iStatusData.heatingMatStatus = RTE_TYPE_HEATING_STATUS_OFF;
    }
    (void)Rte_Write_HeatingMatStatus(&HeatingControl_iStatusData);

    /* Prepare heating level response */
    HeatingControl_iLevelResponse.heatingLevelRes = (Rte_HeatingLevelType)HeatingControl_RuntimeData.currentLevel;
    (void)Rte_Write_HeatingLevelResponse(&HeatingControl_iLevelResponse);

    /* Prepare current temperature response */
    HeatingControl_iTempResponse.currentTemperature = (uint8)(HeatingControl_RuntimeData.currentTemperature / HEATINGCONTROL_TEMP_CONVERSION_FACTOR);
    (void)Rte_Write_CurrentTemperature(&HeatingControl_iTempResponse);

    /* Prepare remaining time response */
    if (HeatingControl_RuntimeData.currentState == HMC_STATE_ACTIVE)
    {
        HeatingControl_iTimeResponse.remainingTime =
            (uint16)((HeatingControl_Config.maxHeatingTime - HeatingControl_RuntimeData.heatingTimer) / HEATINGCONTROL_MINUTES_PER_HOUR);
    }
    else
    {
        HeatingControl_iTimeResponse.remainingTime = HEATINGCONTROL_INIT_PWM_DUTY;
    }
    (void)Rte_Write_RemainingTime(&HeatingControl_iTimeResponse);
}

/**************************************************************************************************
** Function name    : HeatingControl_MapSafetyFaults
** Description      : Map SafetyMonitor fault types to HeatingControl fault types
** Parameter index  : safetyFaults - SafetyMonitor fault mask
** Return value     : HeatingControl_FaultType - Mapped HeatingControl fault mask
** Remarks          : Maps SafetyMonitor faults to corresponding HeatingControl faults
**************************************************************************************************/
STATIC FUNC(HeatingControl_FaultType, HEATINGCONTROL_CODE) HeatingControl_MapSafetyFaults(VAR(uint16, HEATINGCONTROL_VAR) safetyFaults)
{
    VAR(HeatingControl_FaultType, HEATINGCONTROL_VAR) HeatingControl_iMappedFaults = HEATING_CONTROL_FAULT_NONE;

    /* Map temperature faults */
    if ((safetyFaults & SAFETY_FAULT_TEMP_OVER_UPPER) != 0U)
    {
        HeatingControl_iMappedFaults |= HEATING_CONTROL_FAULT_TEMPERATURE_OVER_LIMIT;
    }

    if ((safetyFaults & SAFETY_FAULT_TEMP_UNDER_LOWER) != 0U)
    {
        HeatingControl_iMappedFaults |= HEATING_CONTROL_FAULT_TEMPERATURE_OVER_LIMIT;
    }

    /* Map current faults */
    if ((safetyFaults & SAFETY_FAULT_CURRENT_OPEN_CIRCUIT) != 0U)
    {
        HeatingControl_iMappedFaults |= HEATING_CONTROL_FAULT_HEATING_MAT_OPEN;
    }

    if ((safetyFaults & SAFETY_FAULT_CURRENT_SHORT_CIRCUIT) != 0U)
    {
        HeatingControl_iMappedFaults |= HEATING_CONTROL_FAULT_HEATING_MAT_SHORT;
    }

    /* Map voltage faults */
    if ((safetyFaults & SAFETY_FAULT_VOLTAGE_OVER_LIMIT) != 0U)
    {
        HeatingControl_iMappedFaults |= HEATING_CONTROL_FAULT_VOLTAGE_OVER_LIMIT;
    }

    if ((safetyFaults & SAFETY_FAULT_VOLTAGE_UNDER_LIMIT) != 0U)
    {
        HeatingControl_iMappedFaults |= HEATING_CONTROL_FAULT_VOLTAGE_UNDER_LIMIT;
    }

    return HeatingControl_iMappedFaults;
}